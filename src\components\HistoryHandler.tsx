import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';

const HistoryHandler = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const auth = useAuth();

  useEffect(() => {
    // Handle popstate (back/forward button) events
    const handlePopState = (event: PopStateEvent) => {
      // If we're on the auth loading page and user presses back
      if (location.pathname === '/auth/loading' || location.pathname === '/auth/callback') {
        // Check if we're in an authentication flow
        const isInAuthFlow = window.location.search.includes('code=');
        
        if (!isInAuthFlow) {
          console.log("🔄 Back button pressed during auth loading, redirecting to home");
          event.preventDefault();
          window.location.replace('/');
        }
      }
      
      // If we're on a protected route and not authenticated
    const isProtectedRoute = !location.pathname.match(/^\/(login|signin|signup|forgot-password)?$/);

      if (isProtectedRoute && !auth.isAuthenticated && !auth.isLoading) {
        console.log("🔄 Back navigation to protected route while not authenticated");
        event.preventDefault();
        navigate('/', { replace: true });
      }
    };

    window.addEventListener('popstate', handlePopState);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [location, navigate, auth.isAuthenticated, auth.isLoading]);

  return null;
};

export default HistoryHandler;