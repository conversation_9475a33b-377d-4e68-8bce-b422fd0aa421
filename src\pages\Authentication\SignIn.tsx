import { useAuth } from 'react-oidc-context';
import { useState } from 'react';
import Logo from '../../images/logo/Logo H.svg';
import ProductLogo from '../../images/logo/product-white-logo.png';
import './SignIn.css';
import config from '../../config';


function SignIn() {
  const auth = useAuth();
  const [error, setError] = useState('');

  const handleLogin = () => {
    console.log("🔍 Login clicked, clearing any stale state");

    // Clear any potential stale browser state
    try {
      // Clear all localStorage items that might interfere
      Object.keys(localStorage).forEach(key => {
        if (key.includes('auth') || key.includes('oidc') || key.includes('user') || key.includes('pkce')) {
          localStorage.removeItem(key);
          console.log("🧹 Cleared localStorage:", key);
        }
      });

      // Clear sessionStorage items that might interfere
      Object.keys(sessionStorage).forEach(key => {
        if (key.includes('auth') || key.includes('oidc') || key.includes('user')) {
          sessionStorage.removeItem(key);
          console.log("🧹 Cleared sessionStorage:", key);
        }
      });
      
      // Clear any browser history state that might be causing issues
      if (window.history && window.history.pushState) {
        window.history.pushState({}, document.title, window.location.pathname);
      }
    } catch (e) {
      console.log("🔍 Storage cleanup error:", e);
    }

    // If somehow already authenticated, clear state first
    if (auth.isAuthenticated) {
      console.log("🔄 Already authenticated, signing out first");
    const { clientId, signoutUri } = config.cognito;
auth.signoutRedirect({
  extraQueryParams: {
    client_id: clientId,
    logout_uri: signoutUri,
    response_type: 'code',
  },
});

    } else {
      console.log("🔄 Starting sign-in redirect");
      auth.signinRedirect();
    }
  };

  return (
    <div className="flex flex-col md:flex-row h-screen">
      {/* Left Section */}
      <div className="left-section flex flex-col justify-center text-white md:w-1/2 p-6 md:p-12">
         <img src={ProductLogo} alt="CMMCGenie Logo" className="product-logo" />
        <h2 className="left-section-h1-c flex items-center justify-start">
          <span className="ml-6 left-section-h1-span-level-1 mr-2">
            Level 1
          </span>
          <span className="left-section-h1-span-assessment">Assessment</span>
        </h2>
        <p className="left-section-welcome mt-6 text-sm md:text-lg mb-4">
          Welcome!
        </p>
        <p className="left-section-p text-sm md:text-lg mb-8">
          Please log in to begin or continue your assessment. Your responses
          will be saved securely, and you can return to complete the assessment
          at any time.
        </p>

        {/* Pricing Info */}
<div className=" bg-white bg-opacity-10 backdrop-blur-md border border-white border-opacity-20 rounded-lg p-4 text-sm md:text-base">
  <h3 className="font-semibold text-white mb-1">Pricing</h3>
  <p className="text-white">
    <span className="text-2xl font-bold">$300</span> per assessment
  </p>
  <p className="text-gray-200 mt-1">Cyber Compliance Made Simple — One Time Yearly Fee.</p>
</div>

      </div>

      {/* Right Section */}
      <div className="bg-white flex flex-col items-center justify-center w-full md:w-1/2 relative p-4 md:p-0">
        <div className="absolute top-4 md:top-10 flex justify-between items-center space-x-4 md:space-x-8 w-full px-6">
          <img src={Logo} alt="Raaz Security Logo" className="logo" />
          <div className="flex items-center space-x-4">
            <div className="divider w-px h-6"></div>
            <p className="right-section-automation text-xs md:text-sm text-gray-600 text-center">
              Automation Experts in Cybersecurity
            </p>
          </div>
        </div>

        {/* Login Form */}
        <div className="w-full max-w-md px-6 mt-15">
          <h1 className="text-xl md:text-2xl font-bold mb-2">Login</h1>
          <p className="text-xs md:text-sm text-gray-600 mb-6">
            To Begin or Continue Your CMMC Assessment.
          </p>
          {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
          {auth.isAuthenticated ? (
            <div></div>
          ) : (
            <div>
                  <button
  onClick={handleLogin}
   className="bg-[#002574] text-white px-6 py-2 rounded-2xl font-semibold shadow-sm hover:bg-[#001A50] transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#002574]"
  title="Click to sign in or sign up"
>
  Log in or Sign up
</button>
             
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default SignIn;
