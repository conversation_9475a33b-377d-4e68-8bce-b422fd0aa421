/// <reference types="vite/client" />

interface ImportMetaEnv {
    readonly VITE_API_BASE_URL: string;
    readonly VITE_API_WELCOME: string;
    readonly VITE_API_START_ASSESSMENT: string;
    readonly VITE_API_LEVELS: string;
    readonly VITE_API_LEVEL_QUESTIONS: string;
    readonly VITE_API_PROGRESS: string;
    readonly VITE_API_UPDATE_PROGRESS: string;
    // Add more variables as needed
  }
  
  interface ImportMeta {
    readonly env: ImportMetaEnv;
  }
  
 
  