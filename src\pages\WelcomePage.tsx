import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import Logo from '../images/logo/Logo H.svg';
import ProductLogo from '../images/logo/CMMCGenie.png';
import ABLogo from '../images/logo/AB_logo.png';
import PoweredBy from '../images/logo/Raaz_Powered.png';
import reportIcon from '../images/instructions/report.png';
import reliableIcon from '../images/instructions/reliable.png';
import SecurityIcon from '@mui/icons-material/Security';
import HeadsetMicIcon from '@mui/icons-material/HeadsetMic';
import EmailIcon from '@mui/icons-material/Email';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';

const WelcomePage: React.FC = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  const [currentSet, setCurrentSet] = useState(0);

  const slides = [
    {
      icon: <SecurityIcon className="text-[#002574] w-6 h-6" />,
      title: 'CMMC Level 1 Self-Assessment',
      description:
        'Step-by-step checklist with audio guidance for each practice',
    },
    {
      icon: <VerifiedUserIcon className="text-[#002574] w-6 h-6" />,
      title: 'Confidently Meet Compliance',
      description: 'Stay compliant and aligned with CMMC 2.0 requirements',
    },
    {
      icon: <img src={reportIcon} alt="Report Icon" className="w-6 h-6" />,
      title: 'Instant Report & Insights',
      description: 'Get a detailed breakdown of your compliance status',
    },
    {
      icon: <img src={reliableIcon} alt="Reliable Icon" className="w-6 h-6" />,
      title: 'Simple and Easy to Use',
      description: 'Guidance provided for each domain',
    },
    {
      icon: <SecurityIcon className="text-[#002574] w-6 h-6" />,

      title: 'For Contractors with FCI',
      description: 'Ideal for Small and Medium Sized Business',
    },
    {
      icon: <HeadsetMicIcon className="text-[#002574] w-6 h-6" />,
      title: 'Utilize certified RPO organization',
      description: 'Free 30 minutes consulting for CMMC Guidance',
    },
  ];

  // Group into sets of 3 cards per slide
  const chunkedSlides = [];
  for (let i = 0; i < slides.length; i += 3) {
    chunkedSlides.push(slides.slice(i, i + 3));
  }

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleLogin = () => {
    try {
      Object.keys(localStorage).forEach((key) => {
        if (
          key.includes('auth') ||
          key.includes('oidc') ||
          key.includes('user') ||
          key.includes('pkce')
        ) {
          localStorage.removeItem(key);
        }
      });
      Object.keys(sessionStorage).forEach((key) => {
        if (
          key.includes('auth') ||
          key.includes('oidc') ||
          key.includes('user')
        ) {
          sessionStorage.removeItem(key);
        }
      });
      window.history.pushState({}, document.title, window.location.pathname);
    } catch (e) {
      console.log('Storage cleanup error:', e);
    }

    auth.isAuthenticated ? auth.signoutRedirect() : auth.signinRedirect();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Section - White Background */}
      <div className="bg-[#F8F8F8]">
        {/* Header */}
        <header className="flex max-w-[77rem]  rounded-[1.5rem] bg-[#FFFFFF] items-center justify-between px-6 py-4 max-w-7xl mx-auto">
          <img src={ProductLogo} alt="CMMC Genie Logo" className="h-8" />
          <div className="flex gap-3">
            <button
              onClick={() =>
                auth.isAuthenticated
                  ? auth.signinRedirect()
                  : navigate('/login')
              }
              className="bg-[#00C1A0] text-white px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors whitespace-nowrap flex items-center gap-2"
            >
              Get Started
              <svg
                className="w-4 h-4 text-white transform rotate-45"
                fill="currentColor"
                viewBox="0 0 20 20"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 3.293a1 1 0 011.414 0l5 5a1 1 0 01-1.414 1.414L12 6.414V17a1 1 0 11-2 0V6.414L5.707 9.707A1 1 0 014.293 8.293l5-5z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
            <button
              onClick={handleLogin}
              className="text-gray-600 rounded-[1.5rem] border border-[#0144D4] px-8 text-sm"
            >
              Log in
            </button>
          </div>
        </header>

        {/* Hero Section */}
        <div className="max-w-[77rem] z-50 rounded-[1.5rem] mt-4 bg-[#FFFFFF] mx-auto px-6 py-12 relative -mb-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div>
              <h1 className="text-4xl lg:text-5xl font-bold  mb-6 leading-tight">
                <h1 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  <span className="text-[#002574]">Your Easy CMMC Level 1</span>
                  <br />
                  <span className="text-[#00C1A0]">Self-Assessment Tool</span>
                </h1>
              </h1>
              <p className="text-lg text-[#3F3F3F] mb-8 leading-relaxed">
                CMMCgenie™ by Raaz Security is a user-friendly, guided
                self-assessment tool designed to help your organization meet
                CMMC Level 1 requirements quickly and confidently.
              </p>

              <button
                onClick={() =>
                  auth.isAuthenticated
                    ? auth.signinRedirect()
                    : navigate('/login')
                }
                className="bg-gradient-to-r from-[#002574] to-[#0046DA] text-white px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors whitespace-nowrap flex items-center gap-2"
              >
                <svg
                  className="w-4 h-4 text-white transform rotate-45"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M10.293 3.293a1 1 0 011.414 0l5 5a1 1 0 01-1.414 1.414L12 6.414V17a1 1 0 11-2 0V6.414L5.707 9.707A1 1 0 014.293 8.293l5-5z"
                    clipRule="evenodd"
                  />
                </svg>
                Get Started
              </button>

              {/* Trust Badges */}
              <div className="flex items-center gap-4 mt-8">
                <div className="flex items-center gap-2">
                  <img
                    src={PoweredBy}
                    alt="Raaz Powered By Logo"
                    className="h-24 object-contain"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <img
                    src={ABLogo}
                    alt="Raaz Security Logo"
                    className="h-24 object-contain"
                  />
                </div>
              </div>
            </div>

            {/* Right Content - Video */}
            <div className="relative">
              <div className="aspect-video bg-black rounded-2xl overflow-hidden shadow-2xl">
                <iframe
                  className="w-full h-full"
                  src="https://cmmc-video.s3.us-east-1.amazonaws.com/Demo+Video+Raaz+Security+1.mp4"
                  title="CMMC Compliance Overview"
                  frameBorder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Blue Section as Carousel */}
      <div className="bg-[#002574] text-white py-16 relative">
        <div className="max-w-7xl mx-auto px-6">
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold">
              Why <span className="text-[#00C1A0]">CMMCGenie?</span>
            </h2>
          </div>

          {/* Carousel Wrapper */}
          <div className="relative">
            <div className="overflow-hidden">
              <div
                className="flex transition-transform duration-500"
                style={{ transform: `translateX(-${currentSet * 100}%)` }}
              >
                {chunkedSlides.map((group, idx) => (
                  <div
                    key={idx}
                    className="min-w-full flex flex-col sm:flex-row gap-6"
                  >
                    {group.map((slide, index) => (
                      <div
                        key={index}
                        className="w-full sm:w-1/3 bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
                      >
                        <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center mb-4">
                          {slide.icon}
                        </div>
                        <h3 className="text-xl font-semibold mb-3">
                          {slide.title}
                        </h3>
                        <p className="text-white/80 text-sm leading-relaxed">
                          {slide.description}
                        </p>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={() =>
                setCurrentSet((prev) =>
                  prev === 0 ? chunkedSlides.length - 1 : prev - 1,
                )
              }
              className="absolute top-1/2 left-0 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 p-2 rounded-full transition-colors"
            >
              ‹
            </button>
            <button
              onClick={() =>
                setCurrentSet((prev) => (prev + 1) % chunkedSlides.length)
              }
              className="absolute top-1/2 right-0 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 p-2 rounded-full transition-colors"
            >
              ›
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 mb-4 space-x-2">
            {chunkedSlides.map((_, idx) => (
              <button
                key={idx}
                onClick={() => setCurrentSet(idx)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  idx === currentSet ? 'bg-white' : 'bg-white/40'
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section - Overlapping with Blue Section */}
      <div className="relative -mt-16">
        <div className="max-w-6xl mx-auto px-6">
          <div className="bg-[#00C1A0] rounded-[1.5rem] py-6 px-8 mx-4 md:mx-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              <div className="text-center md:text-left">
                <h3 className="text-2xl font-bold text-white mb-2">
                  Cyber Compliance Made Simple — One-Time Yearly Fee
                </h3>
                <p className="text-white/90 text-lg">
                  Our base plan starts at just $300 — giving you full access to
                  the CMMC Level 1 self-assessment tool, compliance report, and
                  a 30-minute expert consultation post sign-up.
                </p>
              </div>
              <button
               onClick={() =>
                  auth.isAuthenticated
                    ? auth.signinRedirect()
                    : navigate('/login')
                }
                className="bg-gradient-to-r from-[#002574] to-[#0046DA] text-white px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors whitespace-nowrap flex items-center gap-2"
              >
                <svg
                  className="w-4 h-4 text-white transform rotate-45"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M10.293 3.293a1 1 0 011.414 0l5 5a1 1 0 01-1.414 1.414L12 6.414V17a1 1 0 11-2 0V6.414L5.707 9.707A1 1 0 014.293 8.293l5-5z"
                    clipRule="evenodd"
                  />
                </svg>
                Take the assessment
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white mt-6  pt-10 pb-6">
        <div className="max-w-7xl mx-auto px-6 space-y-6">
          {/* Top Line: Logos, Info, Support */}
          <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-8">
            {/* Logo Section */}
            <div className="flex items-center gap-4">
              <img
                src={Logo}
                alt="Raaz Security Logo"
                className="h-8 object-contain"
              />
            </div>

            {/* Description */}
            <div className="text-sm text-gray-600 text-center md:text-left space-y-1">
              <div className="flex justify-center">
                <img
                  src={ProductLogo}
                  alt="CMMC Genie Logo"
                  className="h-6 object-contain"
                />
              </div>
              <p>
                <em>CMMCgenie</em> is a registered trademark of Raaz Security
                Services, Inc.
              </p>
            </div>

            {/* Support Section */}
            <div className="flex items-center justify-center md:justify-end gap-2 text-sm text-gray-600">
              <EmailIcon
                className="w-4 h-4 text-[#00C1A0]"
                aria-hidden="true"
              />
              <div className="flex flex-col-reverse items-start gap-0.5">
                <a
                  href="mailto:<EMAIL>"
                  className="text-[#002574] hover:underline"
                >
                  <EMAIL>
                </a>
                <span>Need help?</span>
              </div>
            </div>
          </div>

          {/* Divider */}
          <hr className="border-gray-300" />

          {/* Bottom Line: Copyright + Links */}
          <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-600 gap-4">
            <p className="text-center md:text-left  text-[#002574]">
              &copy; {new Date().getFullYear()} Raaz Security. All rights
              reserved.
            </p>
            <div className="flex gap-4">
              <a
                href="/privacy-policy"
                className="hover:text-[#002574] hover:underline"
              >
                Privacy Policy
              </a>
              <a
                href="/terms-of-service"
                className="hover:text-[#002574] hover:underline"
              >
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default WelcomePage;
