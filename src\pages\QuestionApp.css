

.h-white {
    color: white !important;
    
    font-size: 14px; /* Default font size for larger screens (1920x1080) */
    font-weight: 700;
    line-height: 21px; /* Default line height */
    text-align: left; /* Default alignment */
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
 
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .h-white {
      font-size: 13px; /* Slightly smaller font size */
      line-height: 19.5px; /* Adjusted line height */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .h-white {
      font-size: 12px; /* Further reduced font size */
      line-height: 18px; /* Adjusted line height */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .h-white {
      font-size: 10px; /* Smallest font size for phones */
      line-height: 15px; /* Adjusted line height */
      text-align: center; /* Center align for better readability on small screens */
    }
  }

  .of-d {
    
    font-size: 14px; /* Default font size for larger screens (1920x1080) */
    font-weight: 400;
    line-height: 21px; /* Default line height */
    text-align: left; /* Default alignment */
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #16E8C4 !important; /* Ensure the correct color */
  }
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .of-d {
      font-size: 13px; /* Slightly smaller font size */
      line-height: 20px; /* Adjusted line height */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .of-d {
      font-size: 12px; /* Further reduced font size */
      line-height: 18px; /* Adjusted line height */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .of-d {
      font-size: 10px; /* Smallest font size for phones */
      line-height: 15px; /* Adjusted line height */
      text-align: center; /* Center align for better readability on small screens */
    }
  }

  .contact {
    width: 100%; /* Full width for small screens */
    max-width: 300px; /* Limit the maximum width */
    height: auto; /* Adjust height based on content */
    border-radius: 13px;
    background: rgba(0, 193, 160, 0.68); /* Background for the container */
    padding: 16px; /* Consistent padding */
    display: flex; /* Flex layout for centering */
    flex-direction: column; /* Aligns child items vertically */
    align-items: center; /* Centers child items horizontally */
    justify-content: center; /* Centers child items vertically */
    margin: 0 auto; /* Center the container horizontally */
  }
  
  .btn-contact {
    width: 100%; /* Full width for responsive design */
    max-width: 250px; /* Limit the button width */
    height: 54px;
    border-radius: 5px;
    background: #FFFFFF;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
  }
  
  .btn-contact:hover {
    background: #e0f7fa; /* Subtle hover effect */
  }
  
  .l-p {
    font-size: 14px; /* Default size */
    color: #fff;
    margin-bottom: 8px;
  }
  
  .l-h {
    color: #FFF;
    
    font-size: 16px;
    font-weight: 700;
    line-height: normal;
    
  }
  
  @media (max-width: 768px) {
    .contact {
      max-width: 90%; /* Reduce width for tablets and smaller screens */
      padding: 12px; /* Adjust padding */
    }
  
    .btn-contact {
      height: 50px; /* Adjust button height for smaller screens */
    }
  
    .l-p {
      font-size: 13px; /* Slightly smaller text for compact layout */
    }
  
    .l-h {
      font-size: 15px; /* Adjust heading size for smaller screens */
    }
  }
  
  @media (max-width: 480px) {
    .contact {
      max-width: 100%; /* Full width for mobile */
      padding: 10px; /* Reduce padding further */
    }
  
    .btn-contact {
      height: 48px; /* Adjust button height for mobile */
    }
  
    .l-p {
      font-size: 12px; /* Smallest text size for very small screens */
    }
  
    .l-h {
      font-size: 14px; /* Adjust heading size for mobile */
    }
  }
  
  

  .q-text {
    color: #000; /* Default text color */
    
    font-size: 18px; /* Default font size for larger screens (1920x1080) */
    font-style: normal;
    font-weight: 400;
    line-height: 1.5; /* Improved readability with proportional line height */
  }
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .q-text {
      font-size: 16px; /* Slightly smaller font size */
      line-height: 1.5; /* Maintain proportional line height */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .q-text {
      font-size: 14px; /* Further reduced font size */
      line-height: 1.4; /* Adjusted line height */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .q-text {
      font-size: 12px; /* Smallest font size for phones */
      line-height: 1.4; /* Adjusted line height */
      text-align: center; /* Center align for better readability on small screens */
    }
  }

  .q-progress {
    color: #3F3F3F;
    
    font-size: 14px; /* Default font size for larger screens (1920x1080) */
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .q-progress {
      font-size: 13px; /* Slightly reduced font size */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .q-progress {
      font-size: 12px; /* Further reduced font size */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .q-progress {
      font-size: 10px; /* Smallest font size for phones */
      text-align: center; /* Center align for readability on narrow screens */
    }
  }
  
  .p-b {
    border-radius: 4px; /* Default for all screen sizes */
    border: 1px solid #FC7D4A; /* Default border color for larger screens */
    padding: 4px; /* Add padding for consistency */
  }
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .p-b {
      padding: 3px; /* Slightly reduced padding */
      border-width: 0.9px; /* Adjusted border thickness */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .p-b {
      padding: 2px; /* Further reduced padding */
      border-width: 0.8px; /* Adjusted border thickness */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .p-b {
      padding: 1px; /* Minimal padding for smaller screens */
      border-width: 0.7px; /* Thin border for compact screens */
      text-align: center; /* Center align for smaller screens */
    }
  }

  /* Default styles for 1920x1080 */
.doc-text {
  color: #8F8F8F;
  
  font-size: 14px; /* Default font size */
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.btn-next {
  width: 180px; /* Default width */
  height: 61px; /* Default height */
  flex-shrink: 0;
  border-radius: 5px;
  background: #002B76;
}

.btn-back {
  width: 180px; /* Default width */
  height: 61px; /* Default height */
  flex-shrink: 0;
  border-radius: 5px;
  background: #CBCBCB;
}

/* Medium screens (1280px - 1919px) */
@media (max-width: 1919px) and (min-width: 1280px) {
  .doc-text {
    font-size: 13px; /* Slightly reduced font size */
  }
  .btn-next,
  .btn-back {
    width: 160px; /* Reduced button width */
    height: 56px; /* Reduced button height */
  }
}

/* Small screens (768px - 1279px) */
@media (max-width: 1279px) and (min-width: 768px) {
  .doc-text {
    font-size: 12px; /* Further reduced font size */
  }
  .btn-next,
  .btn-back {
    width: 140px; /* Further reduced button width */
    height: 50px; /* Further reduced button height */
  }
}

/* Very small screens (less than 768px) */
@media (max-width: 767px) {
  .doc-text {
    font-size: 11px; /* Smallest font size for readability */
    text-align: center; /* Center align for narrow screens */
  }
  .btn-next,
  .btn-back {
    width: 120px; /* Compact width for small screens */
    height: 45px; /* Compact height for small screens */
  }
}

/* Default styles for 1920x1080 */
.f-domain {
  color: #BEBEBE;
  
  font-size: 16px; /* Default font size */
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.f-of-domain {
  color: #CDCDCD;
  
  font-size: 16px; /* Default font size */
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

/* Medium screens (1280px - 1919px) */
@media (max-width: 1919px) and (min-width: 1280px) {
  .f-domain,
  .f-of-domain {
    font-size: 14px; /* Reduced font size */
  }
}

/* Small screens (768px - 1279px) */
@media (max-width: 1279px) and (min-width: 768px) {
  .f-domain,
  .f-of-domain {
    font-size: 12px; /* Further reduced font size */
  }
}

/* Very small screens (less than 768px) */
@media (max-width: 767px) {
  .f-domain,
  .f-of-domain {
    font-size: 10px; /* Smallest font size for compact screens */
    text-align: center; /* Center-align for better readability */
  }
}

/* Default styles for 1920x1080 */
.l-g {
  color: #16E8C4;
  
  font-size: 14px; /* Default font size */
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

/* Medium screens (1280px - 1919px) */
@media (max-width: 1919px) and (min-width: 1280px) {
  .l-g {
    font-size: 13px; /* Slightly reduced font size */
  }
}

/* Small screens (768px - 1279px) */
@media (max-width: 1279px) and (min-width: 768px) {
  .l-g {
    font-size: 12px; /* Further reduced font size */
  }
}

/* Very small screens (less than 768px) */
@media (max-width: 767px) {
  .l-g {
    font-size: 10px; /* Smallest font size for compact screens */
    text-align: center; /* Center-align for better readability */
  }
}

.l-flex{
  color: #FFF;

font-size: 11px;
font-style: normal;
font-weight: 400;
line-height: normal;
}

.l-f-b{
  color: #FFF;

font-size: 16px;
font-style: normal;
font-weight: 700;
line-height: normal;
}

 /* Divider */
 .divider {
  width: 3px; /* Default width for larger screens (1920x1080) */
  height: 50px; /* Default height */
  background: #00c1a0; /* Divider color */
  gap: 0px;
  opacity: 0px;
  position: relative;
}

.big-d{
flex-shrink: 0;
color: url(<path-to-image>) lightgray 0px 5.92px / 100% 80.754% no-repeat;
}
