import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';

interface AddEditQuestionsProps {
  open: boolean;
  onClose: () => void;
  onSave: (question: { questionId: number; questionText: string; questionOrder: number }) => void;
  questionToEdit: { questionId: number; questionText: string; questionOrder: number } | null;
}

const AddEditQuestions: React.FC<AddEditQuestionsProps> = ({
  open,
  onClose,
  onSave,
  questionToEdit,
}) => {
  const [questionText, setQuestionText] = useState<string>('');
  const [questionOrder, setQuestionOrder] = useState<number>(0);

  useEffect(() => {
    if (questionToEdit) {
      setQuestionText(questionToEdit.questionText);
      setQuestionOrder(questionToEdit.questionOrder);
    } else {
      setQuestionText('');
      setQuestionOrder(0);
    }
  }, [questionToEdit]);

  const handleSubmit = () => {
    if (questionText.trim()) {
      const newQuestion = questionToEdit
        ? { questionId: questionToEdit.questionId, questionText, questionOrder } // Editing
        : { questionId: Date.now(), questionText, questionOrder }; // Adding new question with unique ID
      onSave(newQuestion);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      sx={{
        width: '100%',
        maxWidth: '70%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        margin: 'auto',
      }}
    >
      <DialogTitle>{questionToEdit ? 'Edit Question' : 'Add Question'}</DialogTitle>
      
      <DialogContent>
        <TextField
          label="Question"
          fullWidth
          multiline
          minRows={3}
          maxRows={10}
          value={questionText}
          onChange={(e) => setQuestionText(e.target.value)}
          margin="normal"
        />
        <TextField
          label="Question Order"
          fullWidth
          type="number"
          value={questionOrder}
          onChange={(e) => setQuestionOrder(Number(e.target.value))}
          margin="normal"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} color="primary">
          {questionToEdit ? 'Update' : 'Add'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddEditQuestions;
