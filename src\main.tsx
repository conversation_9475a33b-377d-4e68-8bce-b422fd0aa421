import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer as Router } from 'react-router-dom';
import App from './App';
import './css/style.css';
import './css/satoshi.css';
import 'jsvectormap/dist/css/jsvectormap.css';
import 'flatpickr/dist/flatpickr.min.css';

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <Router basename="/assessment">   
    {/* For Test Link */} 
    {/* <Router basename="/">   */}
    {/* For Production Link */}
     
      <App />
    </Router>
  </React.StrictMode>
);


