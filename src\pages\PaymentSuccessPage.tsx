import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import DefaultLayout from '../layout/DefaultLayout';
import { useAuth } from 'react-oidc-context';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';
import './PaymentPage.css';
import config from '../config';

const PaymentSuccessPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const auth = useAuth();
  const [status, setStatus] = useState<'success' | 'pending' | 'processing'| null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    // setErrorMessage("User is not authenticated. Cannot fetch token.");
    return null;
  };

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const transactionId = queryParams.get('transactionId');

    if (!transactionId) {
      // setErrorMessage('Transaction ID not found in URL.');
      navigate('/payment');
      return;
    }

    const fetchPaymentStatus = async () => {
      const token = await getAccessToken();
      //   if (!token) {
      //     setErrorMessage("Failed to retrieve access token.");
      //     return;
      //   }

      try {
        const response = await fetch(
          `${config.userService.paymentStatus}/${transactionId}`,
          {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${token}`,
              accept: 'text/plain', // Expecting plain text
            },
          },
        );

        if (!response.ok) {
          throw new Error(
            `Failed to fetch payment status: ${response.status} - ${response.statusText}`,
          );
        }

        const textResponse = await response.text(); // Read as plain text
        console.log('Payment Status Response:', textResponse);

        // Handle different statuses from the API
        if (textResponse.toLowerCase() === 'completed') {
          setStatus('success');
        } else if (textResponse.toLowerCase() === 'processing') {
          setStatus('processing');
        } else {
          setStatus('pending');
        }
      } catch (error: any) {
        // setErrorMessage("Error fetching payment status: " + error.message);
      }
    };

    fetchPaymentStatus();
  }, [auth.isAuthenticated, location.search]);

  const handleStartAssessment = async () => {
    const token = await getAccessToken();
    try {
      const response = await fetch(`${config.userService.startAssessment}`, {
        method: 'POST',
        headers: {
          accept: '*/*',
          Authorization: `Bearer ${token}`,
        },
        body: '', // Empty body as per your curl command
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      console.log('Assessment started successfully.');
      navigate('/dashboard'); // Navigate only after a successful API response
    } catch (error) {
      console.error('Error starting assessment:', error);
    }
  };

  return (
    <DefaultLayout>
      <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md text-center">
        {errorMessage && (
          <div className="bg-red-100 text-red-700 p-4 rounded-lg mb-4">
            <p>{errorMessage}</p>
          </div>
        )}

        {status === 'success' && (
          <>
            <div className="flex flex-col items-center justify-center text-center space-y-4">
              <div className="mx-auto flex items-center justify-center w-24 h-24 rounded-full bg-blue-100">
                <ThumbUpAltIcon
                  style={{
                    fontSize: 60,
                    color: 'rgb(0, 37, 116, var(--tw-text-opacity))',
                  }}
                />
              </div>
              <h1 className="heading text-3xl font-bold text-gray-800">
                You're All Set!
              </h1>
              <p className="para text-gray-600">
                Thank you for your payment. Your transaction is successful!. Start answering the questions
                to assess your compliance readiness.
              </p>
              <button
                className="relative flex items-center text-white px-6 py-3 rounded-lg shadow-md transition-all blue-btn"
                onClick={handleStartAssessment}
              >
                Start Assessment
              </button>
            </div>
          </>
        )}

        {status === 'processing' && (
          <>
            <h1 className="heading text-2xl font-bold text-yellow-600">
              Processing Payment...
            </h1>
            <p className="para text-gray-600">
              Your payment is still being processed. Please check back shortly.
            </p>
          </>
        )}

        {status === 'pending' && (
          <>
            <h1 className="heading text-2xl font-bold text-gray-800">
              Payment Pending
            </h1>
            <p className="para text-gray-600">
              Your payment is still in progress. Please refresh the page or wait
              for confirmation.
            </p>
          </>
        )}
      </div>
    </DefaultLayout>
  );
};

export default PaymentSuccessPage;
