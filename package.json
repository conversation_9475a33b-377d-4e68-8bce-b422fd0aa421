{"name": "raaz-security", "private": true, "version": "1.3.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://tailadmin.com"}, "dependencies": {"@azure/msal-browser": "^3.26.1", "@azure/msal-react": "^2.1.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@material-ui/icons": "^4.11.3", "@mui/icons-material": "^6.3.1", "@mui/material": "^6.3.1", "@mui/x-charts": "^7.23.6", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.6.0", "apexcharts": "^3.41.0", "aws-amplify": "^6.10.2", "axios": "^1.7.7", "flatpickr": "^4.6.13", "headlessui": "^0.0.0", "jsvectormap": "^1.5.3", "match-sorter": "^6.3.1", "moment": "^2.30.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.10.1", "react-oidc-context": "^3.2.0", "react-router-dom": "^6.14.2", "react-slick": "^0.30.3", "react-toastify": "^9.1.3", "slick-carousel": "^1.8.1", "sort-by": "^0.0.2", "swiper": "^11.2.1"}, "devDependencies": {"@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "@types/react-slick": "^0.23.13", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "file-loader": "^6.2.0", "postcss": "^8.4.27", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.4.1", "tailwindcss": "^3.4.1", "vite": "^4.5.5", "webpack": "^5.88.2"}}