import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  Box,
  Typography,
  Grid,
  LinearProgress,
  Paper,
  CircularProgress,
  Button,
} from '@mui/material';
import CircularProgressWithLabel from '../components/CircularProgress';
import Bar<PERSON>hart from '../components/BarChart';
import DefaultLayout from '../layout/DefaultLayout';
import config from '../config';
import { useAuth } from 'react-oidc-context';
import { toast, ToastContainer } from 'react-toastify';

const Dashboard = () => {
  interface ProgressItem {
    label: string;
    progress: number;
  }

  const [progressItems, setProgressItems] = useState<ProgressItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [authLoading, setAuthLoading] = useState(true); // Added loading state for auth
  const [overallProgress, setOverallProgress] = useState(0);
  const auth = useAuth();
  const navigate = useNavigate();

  // Function to get the access token

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    console.error('User is not authenticated. Cannot fetch token.');
    return null;
  };

  useEffect(() => {
    const checkAccess = async () => {
      if (!auth.isAuthenticated || !auth.user) {
        console.error('User is not authenticated yet.');
        return;
      }

      try {
        const token = await getAccessToken();
        if (!token) {
          return;
        }

        const response = await fetch(`${config.userService.accessCheck}`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            accept: '*/*',
          },
        });

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json(); // ✅ Parse JSON response

        // ✅ Explicitly check if accessGranted is false
        if (!data.hasAgreedToAgreement) {
          navigate('/user-agreement', { replace: true });
        } else if (!data.org_details_saved) {
          navigate('/org', { replace: true });
        } else if (!data.accessGranted) {
          navigate('/payment', { replace: true });
        }
      } catch (error) {
        console.error('Access check failed:', error);
      }
    };

    if (auth.isAuthenticated && auth.user) {
      checkAccess();
    }
  }, [auth.isAuthenticated, auth.user, navigate]);

  useEffect(() => {
    // Wait until the auth context is initialized
    if (!auth.isLoading) {
      setAuthLoading(false); // Auth context is ready
    }
  }, [auth.isLoading]);

  useEffect(() => {
    if (authLoading) return; // Wait until authentication state is resolved
  
    const fetchProgressData = async () => {
      try {
        setLoading(true);
        const token = await getAccessToken();
        if (!token) return;
  
        // Fetch both overall progress and progress items in parallel
        const [overallProgressResponse, progressItemsResponse] = await Promise.all([
          axios.get(`${config.userService.domainsOverallProgress}`, {
            headers: { accept: '*/*', Authorization: `Bearer ${token}` },
          }),
          axios.get(`${config.userService.domainsProgress}`, {
            headers: { accept: '*/*', Authorization: `Bearer ${token}` },
          })
        ]);
  
        setOverallProgress(overallProgressResponse.data?.overallProgress || 0);
  
        const fetchedProgressItems = progressItemsResponse.data.map((item: any) => ({
          label: item.domainName || 'Unknown',
          progress: item.progress || 0,
        }));
  
        setProgressItems(fetchedProgressItems);
      } catch (error) {
        console.error('Error fetching progress data:', error);
      } finally {
        setLoading(false);
      }
    };
  
    fetchProgressData();
  }, [authLoading]); // Ensure it only runs once when authLoading is resolved
  

  if (authLoading) {
    // Show loading spinner while waiting for auth context to initialize
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  const handleDownloadReport = async () => {
    try {
      const token = await getAccessToken();
      if (!token) return;

      const response = await axios.get(`${config.userService.reportPdf}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          accept: '*/*',
        },
        responseType: 'blob',
      });

      // Get current date and time in YYYY-MM-DD_HH-MM-SS format
      const now = new Date();
      const formattedDate = `${now.getFullYear()}-${String(
        now.getMonth() + 1,
      ).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}_${String(
        now.getHours(),
      ).padStart(2, '0')}-${String(now.getMinutes()).padStart(2, '0')}-${String(
        now.getSeconds(),
      ).padStart(2, '0')}`;

      // Create a download link for the PDF
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Assessment_Report_${formattedDate}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.parentNode?.removeChild(link);

      // ✅ Show success toast
      toast.success('Report downloaded successfully!');
    } catch (error) {
      console.error('Error downloading report:', error);

      // ❌ Show error toast
      toast.error('Failed to download report. Please try again.');
    }
  };

  return (
    <DefaultLayout>
      <ToastContainer
        position="top-center" // Use "bottom-center" for bottom-middle placement
        autoClose={3000} // Auto close after 3 seconds
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      <Box
        sx={{
          px: { xs: 2, sm: 3, md: 4 },
          py: { xs: 2, sm: 3, md: 4 },
          backgroundColor: '#f5f5f5',
          minHeight: '100vh',
        }}
      >
        {/* Header */}

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleDownloadReport}
            startIcon={<i className="material-icons"></i>}
            sx={{
              textTransform: 'none', // Preserve original case
              fontWeight: 'bold',
              fontSize: '14px',
              px: 3,
              py: 1.5,
              boxShadow: 2,
              backgroundColor: '#1976d2',
              '&:hover': {
                backgroundColor: '#155fa0',
              },
            }}
          >
            Download Report
          </Button>
        </Box>

        {/* Wrapped in a white background div */}
        <div className="bg-white shadow-md rounded-lg p-4 sm:p-6 mb-6">
          <p className="text-gray-700 mb-4 text-base sm:text-lg md:text-xl">
            This progress bar represents your overall assessment completion
            percentage. It is calculated based on your progress across all
            security domains. The percentage indicates how much of the
            assessment has been completed successfully.
          </p>

          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: 'center',
              gap: 2,
              mb: { xs: 2, sm: 4 },
            }}
          >
            <p
              className="text-base sm:text-lg md:text-xl font-bold"
              style={{ color: '#002B76' }}
            >
              Assessment Progress:
            </p>

            {/* Progress Bar */}
            <Box sx={{ width: { xs: '100%', sm: '70%' }, maxWidth: '600px' }}>
              <LinearProgress
                variant="determinate"
                value={overallProgress}
                sx={{
                  height: '16px',
                  borderRadius: '8px',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: '#4caf50',
                  },
                }}
              />
            </Box>

            {/* Progress Percentage */}
            <Typography
              variant="body1"
              sx={{
                fontWeight: 'bold',
                textAlign: { xs: 'center', sm: 'left' },
              }}
            >
              {Math.round(overallProgress)}%
            </Typography>
          </Box>
        </div>

        {/* Bar Chart */}
        <Paper
  sx={{
    p: { xs: 2, sm: 3 },
    mb: { xs: 2, sm: 4 },
    boxShadow: 2,
    overflowX: 'auto',
  }}
>
  <div className="mx-auto px-4 py-6">
    <h3 className="text-xl font-semibold text-gray-800 mb-4">Understanding Your Assessment Chart</h3>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="bg-blue-50 p-4 rounded-lg shadow">
        <div className="flex items-center mb-2">
          <svg className="w-6 h-6 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h4 className="text-lg font-medium text-gray-800">Overview</h4>
        </div>
        <p className="text-gray-600">This chart provides a comprehensive view of your assessment status across various security domains.</p>
      </div>
      <div className="bg-green-50 p-4 rounded-lg shadow">
        <div className="flex items-center mb-2">
          <svg className="w-6 h-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
          </svg>
          <h4 className="text-lg font-medium text-gray-800">Bar Representation</h4>
        </div>
        <p className="text-gray-600">Each bar illustrates the number of controls that are <span className="text-blue-600 font-medium">Met</span>, <span className="text-red-600 font-medium">Not Met</span>, or <span className="text-yellow-600 font-medium">Partially Met</span>.</p>
      </div>
      <div className="bg-purple-50 p-4 rounded-lg shadow">
        <div className="flex items-center mb-2">
          <svg className="w-6 h-6 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <h4 className="text-lg font-medium text-gray-800">Detailed Insights</h4>
        </div>
        <p className="text-gray-600">Hover over individual bars to reveal detailed control counts for each category, providing in-depth insights into your security posture.</p>
      </div>
    </div>
  </div>

  <div className="w-full overflow-x-auto">
    <BarChart auth={auth} />
  </div>
</Paper>


      
        {/* Instruction for Circular Progress Indicators */}

  <div className=" mb-4 mx-auto px-4 py-6 bg-white rounded-lg shadow-md">
    <h3 className="text-xl font-semibold text-gray-800 mb-4">Understanding Your Domain Progress</h3>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="bg-blue-50 p-4 rounded-lg shadow-sm">
        <div className="flex items-center mb-2">
          <svg className="w-6 h-6 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
          </svg>
          <h4 className="text-lg font-medium text-gray-800">Security Domains</h4>
        </div>
        <p className="text-gray-600">Each section below represents a specific security domain, helping you focus your assessment efforts effectively.</p>
      </div>
      <div className="bg-green-50 p-4 rounded-lg shadow-sm">
        <div className="flex items-center mb-2">
          <svg className="w-6 h-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h4 className="text-lg font-medium text-gray-800">Progress Indicators</h4>
        </div>
        <p className="text-gray-600">Circular progress indicators visually represent your current progress in each security domain.</p>
      </div>
      <div className="bg-purple-50 p-4 rounded-lg shadow-sm">
        <div className="flex items-center mb-2">
          <svg className="w-6 h-6 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
          </svg>
          <h4 className="text-lg font-medium text-gray-800">Interactive Assessment</h4>
        </div>
        <p className="text-gray-600">Click on any indicator to begin the assessment and explore detailed insights for that specific domain.</p>
      </div>
    </div>
  </div>



        {loading ? (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            height="200px"
          >
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={2}>
            {progressItems.map((item, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Paper
                  sx={{
                    p: { xs: 2, sm: 3 },
                    textAlign: 'center',
                    boxShadow: 1,
                  }}
                >
                  <Box
                    sx={{
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 },
                    }}
                    onClick={() =>
                      navigate(`/domains/${encodeURIComponent(item.label)}`)
                    }
                  >
                    <CircularProgressWithLabel value={item.progress} />
                    <Typography variant="body1" sx={{ mt: { xs: 1, sm: 2 } }}>
                      {item.label}
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
    </DefaultLayout>
  );
};

export default Dashboard;
