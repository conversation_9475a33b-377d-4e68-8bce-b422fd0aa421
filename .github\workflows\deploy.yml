name: Auto-Deploy CI/CD Pipeline (Demo)

on:
  push:
    branches:
      - development # Trigger deployment when pushing to development branch

  release:
    types:
      - published  # ✅ Auto-deploy when a new GitHub release is published.

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Create `.env` File Before Building
        run: |
          echo "Creating .env file before building..."
          echo "${{ secrets.ENV_FILE }}" > .env
          cat .env  # Optional: verify contents

      - name: Install dependencies and build
        run: |
          npm install --force
          npm run build

      - name: Backup Old Deployment on Server
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            echo "Backing up old deployment..."

            if [ -d "/home/<USER>/dist-qu-old" ]; then
              sudo rm -rf /home/<USER>/dist-qu-old/*
            else
              sudo mkdir -p /home/<USER>/dist-qu-old
            fi

            if [ -d "/home/<USER>/dist-qu" ]; then
              sudo mv /home/<USER>/dist-qu/* /home/<USER>/dist-qu-old/
            fi

      - name: Fix Permissions on Server
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            echo "Setting permissions for dist-qu..."
            sudo chmod -R 777 /home/<USER>/dist-qu

      - name: Transfer Built Files to Server
        uses: appleboy/scp-action@v0.1.6
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_KEY }}
          source: ./dist/
          target: /home/<USER>/dist-qu/

      - name: Deploy to Tomcat and Set Up .env
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            echo "Creating .env file on server..."
            echo "${{ secrets.ENV_FILE }}" > /home/<USER>/dist-qu/.env

            echo "Setting correct permissions for .env file..."
            sudo chmod 600 /home/<USER>/dist-qu/.env

            # echo "Cleaning up old assessment deployment...."
            # sudo rm -rf /usr/local/tomcat9/webapps/assessment/*

            echo "Deploying new files from dist-qu/dist to Tomcat assessment directory..."
            sudo cp -R /home/<USER>/dist-qu/dist/* /usr/local/tomcat9/webapps/assessment/

      - name: Verify Deployment
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            echo "Checking if .env exists on server..."
            cat /home/<USER>/dist-qu/.env || echo "ERROR: .env file missing!"

            echo "Checking deployed files in Tomcat assessment directory..."
            ls -l /usr/local/tomcat9/webapps/assessment/
