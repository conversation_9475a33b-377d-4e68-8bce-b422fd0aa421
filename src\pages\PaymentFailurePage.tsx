import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import DefaultLayout from "../layout/DefaultLayout";
import { useAuth } from "react-oidc-context";
import CancelIcon from "@mui/icons-material/Cancel";
import "./PaymentPage.css";
import config from "../config";

const PaymentFailurePage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const auth = useAuth();
  const [status, setStatus] = useState<"cancelled" | "pending" | "processing"| null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    console.log("User is not authenticated. Cannot fetch token.");
    return null;
  };

  const fetchPaymentCancel = async () => {
    // if (!auth.isAuthenticated || !auth.user) {
    // console.log("User is not authenticated.");
    //   return;
    // }

    const queryParams = new URLSearchParams(location.search);
    const transactionId = queryParams.get("transactionId");

    if (!transactionId) {
      setErrorMessage("Transaction ID not found in URL.");
      return;
    }


    const token = await getAccessToken();


    try {
      const response = await fetch(
        `${config.userService.paymentCancelled}?transactionId=${transactionId}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            accept: "text/plain", // API returns plain text
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch payment status: ${response.status} - ${response.statusText}`);
      }

      const textResponse = await response.text(); // Read as plain text
      fetchPaymentStatus();

     
    } catch (error: any) {
      console.log("Error fetching payment status: " + error.message);
    }
  };

  const fetchPaymentStatus = async () => {
    // if (!auth.isAuthenticated || !auth.user) {
    // console.log("User is not authenticated.");
    //   return;
    // }

    const queryParams = new URLSearchParams(location.search);
    const transactionId = queryParams.get("transactionId");

    if (!transactionId) {
      setErrorMessage("Transaction ID not found in URL.");
      return;
    }


    const token = await getAccessToken();


    try {
      const response = await fetch(
        `${config.userService.paymentStatus}/${transactionId}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            accept: "text/plain", // API returns plain text
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch payment status: ${response.status} - ${response.statusText}`);
      }

      const textResponse = await response.text(); // Read as plain text
      console.log("Payment Status Response:", textResponse);

      // Handle different statuses from the API
      if (textResponse.toLowerCase() === "failed") {
        setStatus("cancelled");
      } else if (textResponse.toLowerCase() === "processing") {
        setStatus("processing");
      } else {
        setStatus("pending");
      }
    } catch (error: any) {
      console.log("Error fetching payment status: " + error.message);
    }
  };

  const handleCheckout = async () => {
    const token = await getAccessToken();
    if (!token) {
      console.log("Failed to retrieve access token.");
      return;
    }
    try {
      const response = await fetch(`${config.userService.checkoutSession}`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          accept: "*/*",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to create checkout session: ${response.status} - ${response.statusText}`);
      }

      const { url } = await response.json();

      if (url) {
        window.location.href = url;
      } else {
        console.log("Checkout session URL was not provided by the API.");
      }
    } catch (error: any) {
      console.log(`Error during checkout session creation: ${error.message}`);
    }
  };

  useEffect(() => {
    if (auth.isAuthenticated) {
      fetchPaymentCancel();
    }
  }, [auth.isAuthenticated, location.search]);
  

  return (
    <DefaultLayout>
      <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md text-center">
        {errorMessage && (
          <div className="bg-red-100 text-red-700 p-4 rounded-lg mb-4">
            <p>{errorMessage}</p>
          </div>
        )}

        {status === "cancelled" && (
          <>
            <div className="flex flex-col items-center justify-center text-center space-y-4">
              <div className="mx-auto flex items-center justify-center w-24 h-24 rounded-full bg-red-100">
                <CancelIcon style={{ fontSize: 60, color: "rgb(200, 0, 0)" }} />
              </div>
              <h1 className="heading text-3xl font-bold text-red-600">Payment Cancelled</h1>
              <p className="para text-gray-600">
                Your payment was cancelled. If this was a mistake, you can try again.
              </p>
              <button
                className="relative flex items-center text-white px-6 py-3 rounded-lg shadow-md transition-all bg-red-600 hover:bg-red-700"
                onClick={handleCheckout}
              >
                Try Again
              </button>
            </div>
          </>
        )}

        {status === "processing" && (
          <>
            <h1 className="heading text-2xl font-bold text-yellow-600">Processing Payment...</h1>
            <p className="para text-gray-600">Your payment is still being processed. Please check back shortly.</p>
          </>
        )}

        {status === "pending" && (
          <>
            <h1 className="heading text-2xl font-bold text-gray-800">Payment Pending</h1>
            <p className="para text-gray-600">Your payment is still in progress. Please refresh the page or wait for confirmation.</p>
          </>
        )}
      </div>
    </DefaultLayout>
  );
};

export default PaymentFailurePage;
