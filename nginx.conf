server {
    listen 80;
    server_name localhost;

    root /usr/share/nginx/html/assessment;
    index index.html;

    location / {
        try_files $uri /index.html;
    }

    # ✅ Fix Asset Path (Use `alias`)
    location /assessment/assets/ {
        alias /usr/share/nginx/html/assessment/assets/;
        try_files $uri =404;
        autoindex off;
        expires max;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }

    # ✅ Fix JavaScript & CSS Paths
    location ~* \.(js|mjs|json|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|otf|eot|mp4|webm|ogg|mp3|wav|flac|aac|webp|avif)$ {
        alias /usr/share/nginx/html/assessment/assets/;
        try_files $uri =404;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }

    # ✅ Redirect 404 Errors to React App
    error_page 404 /assessment/index.html;
}
