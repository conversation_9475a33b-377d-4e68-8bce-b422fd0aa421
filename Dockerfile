# Use official Node.js image as base
FROM node:18-alpine AS build
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json ./
RUN npm install --force

# Copy the rest of the files and build
COPY . .
RUN npm run build

# Use Nginx to serve the built app
FROM nginx:alpine
WORKDIR /usr/share/nginx/html

# Create "assessment" folder and copy built React app into it
RUN mkdir -p /usr/share/nginx/html/assessment
COPY --from=build /app/dist /usr/share/nginx/html/assessment/

# Copy the updated Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Ensure permissions are set correctly
RUN chmod -R 755 /usr/share/nginx/html/assessment

# Expose port 80
EXPOSE 80

# Restart Nginx properly
CMD ["nginx", "-g", "daemon off;"]
