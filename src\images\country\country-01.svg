<svg width="21" height="14" viewBox="0 0 21 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_22_27)">
<path d="M18.4739 0.411499H1.55556C0.696446 0.411499 0 1.10794 0 1.96705V12.1893C0 13.0484 0.696446 13.7449 1.55556 13.7449H18.4739C19.333 13.7449 20.0295 13.0484 20.0295 12.1893V1.96705C20.0295 1.10794 19.333 0.411499 18.4739 0.411499Z" fill="white"/>
<mask id="mask0_22_27" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="21" height="14">
<path d="M18.4739 0.411499H1.55556C0.696446 0.411499 0 1.10794 0 1.96705V12.1893C0 13.0484 0.696446 13.7449 1.55556 13.7449H18.4739C19.333 13.7449 20.0295 13.0484 20.0295 12.1893V1.96705C20.0295 1.10794 19.333 0.411499 18.4739 0.411499Z" fill="white"/>
</mask>
<g mask="url(#mask0_22_27)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.0295 0.411499H0V1.30039H20.0295V0.411499ZM20.0295 2.18928H0V3.07817H20.0295V2.18928ZM0 3.96705H20.0295V4.85594H0V3.96705ZM20.0295 5.74484H0V6.63372H20.0295V5.74484ZM0 7.52262H20.0295V8.41152H0V7.52262ZM20.0295 9.30037H0V10.1893H20.0295V9.30037ZM0 11.0781H20.0295V11.9671H0V11.0781ZM20.0295 12.856H0V13.7448H20.0295V12.856Z" fill="#D02F44"/>
<path d="M8.5841 0.411499H0V6.63372H8.5841V0.411499Z" fill="#46467F"/>
<g filter="url(#filter0_d_22_27)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M1.9074 1.74479C1.9074 1.99026 1.69389 2.18924 1.43051 2.18924C1.16713 2.18924 0.953613 1.99026 0.953613 1.74479C0.953613 1.49933 1.16713 1.30035 1.43051 1.30035C1.69389 1.30035 1.9074 1.49933 1.9074 1.74479ZM3.81497 1.74479C3.81497 1.99026 3.60146 2.18924 3.33809 2.18924C3.0747 2.18924 2.86119 1.99026 2.86119 1.74479C2.86119 1.49933 3.0747 1.30035 3.33809 1.30035C3.60146 1.30035 3.81497 1.49933 3.81497 1.74479ZM5.24566 2.18924C5.50903 2.18924 5.72255 1.99026 5.72255 1.74479C5.72255 1.49933 5.50903 1.30035 5.24566 1.30035C4.98228 1.30035 4.76876 1.49933 4.76876 1.74479C4.76876 1.99026 4.98228 2.18924 5.24566 2.18924ZM7.63012 1.74479C7.63012 1.99026 7.41661 2.18924 7.15322 2.18924C6.88985 2.18924 6.67634 1.99026 6.67634 1.74479C6.67634 1.49933 6.88985 1.30035 7.15322 1.30035C7.41661 1.30035 7.63012 1.49933 7.63012 1.74479ZM2.3843 3.07812C2.64768 3.07812 2.86119 2.87914 2.86119 2.63368C2.86119 2.38822 2.64768 2.18924 2.3843 2.18924C2.12092 2.18924 1.9074 2.38822 1.9074 2.63368C1.9074 2.87914 2.12092 3.07812 2.3843 3.07812ZM4.76876 2.63368C4.76876 2.87914 4.55525 3.07812 4.29187 3.07812C4.02849 3.07812 3.81497 2.87914 3.81497 2.63368C3.81497 2.38822 4.02849 2.18924 4.29187 2.18924C4.55525 2.18924 4.76876 2.38822 4.76876 2.63368ZM6.19944 3.07812C6.46282 3.07812 6.67634 2.87914 6.67634 2.63368C6.67634 2.38822 6.46282 2.18924 6.19944 2.18924C5.93606 2.18924 5.72255 2.38822 5.72255 2.63368C5.72255 2.87914 5.93606 3.07812 6.19944 3.07812ZM7.63012 3.52257C7.63012 3.76804 7.41661 3.96702 7.15322 3.96702C6.88985 3.96702 6.67634 3.76804 6.67634 3.52257C6.67634 3.27711 6.88985 3.07814 7.15322 3.07814C7.41661 3.07814 7.63012 3.27711 7.63012 3.52257ZM5.24566 3.96702C5.50903 3.96702 5.72255 3.76804 5.72255 3.52257C5.72255 3.27711 5.50903 3.07814 5.24566 3.07814C4.98228 3.07814 4.76876 3.27711 4.76876 3.52257C4.76876 3.76804 4.98228 3.96702 5.24566 3.96702ZM3.81497 3.52257C3.81497 3.76804 3.60146 3.96702 3.33809 3.96702C3.0747 3.96702 2.86119 3.76804 2.86119 3.52257C2.86119 3.27711 3.0747 3.07814 3.33809 3.07814C3.60146 3.07814 3.81497 3.27711 3.81497 3.52257ZM1.43051 3.96702C1.69389 3.96702 1.9074 3.76804 1.9074 3.52257C1.9074 3.27711 1.69389 3.07814 1.43051 3.07814C1.16713 3.07814 0.953613 3.27711 0.953613 3.52257C0.953613 3.76804 1.16713 3.96702 1.43051 3.96702ZM2.86119 4.41146C2.86119 4.65692 2.64768 4.8559 2.3843 4.8559C2.12092 4.8559 1.9074 4.65692 1.9074 4.41146C1.9074 4.166 2.12092 3.96702 2.3843 3.96702C2.64768 3.96702 2.86119 4.166 2.86119 4.41146ZM4.29187 4.8559C4.55525 4.8559 4.76876 4.65692 4.76876 4.41146C4.76876 4.166 4.55525 3.96702 4.29187 3.96702C4.02849 3.96702 3.81497 4.166 3.81497 4.41146C3.81497 4.65692 4.02849 4.8559 4.29187 4.8559ZM6.67634 4.41146C6.67634 4.65692 6.46282 4.8559 6.19944 4.8559C5.93606 4.8559 5.72255 4.65692 5.72255 4.41146C5.72255 4.166 5.93606 3.96702 6.19944 3.96702C6.46282 3.96702 6.67634 4.166 6.67634 4.41146ZM7.15322 5.74479C7.41661 5.74479 7.63012 5.54581 7.63012 5.30035C7.63012 5.05489 7.41661 4.8559 7.15322 4.8559C6.88985 4.8559 6.67634 5.05489 6.67634 5.30035C6.67634 5.54581 6.88985 5.74479 7.15322 5.74479ZM5.72255 5.30035C5.72255 5.54581 5.50903 5.74479 5.24566 5.74479C4.98228 5.74479 4.76876 5.54581 4.76876 5.30035C4.76876 5.05489 4.98228 4.8559 5.24566 4.8559C5.50903 4.8559 5.72255 5.05489 5.72255 5.30035ZM3.33809 5.74479C3.60146 5.74479 3.81497 5.54581 3.81497 5.30035C3.81497 5.05489 3.60146 4.8559 3.33809 4.8559C3.0747 4.8559 2.86119 5.05489 2.86119 5.30035C2.86119 5.54581 3.0747 5.74479 3.33809 5.74479ZM1.9074 5.30035C1.9074 5.54581 1.69389 5.74479 1.43051 5.74479C1.16713 5.74479 0.953613 5.54581 0.953613 5.30035C0.953613 5.05489 1.16713 4.8559 1.43051 4.8559C1.69389 4.8559 1.9074 5.05489 1.9074 5.30035Z" fill="url(#paint0_linear_22_27)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_22_27" x="-3.04639" y="-1.69965" width="14.6765" height="12.4445" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_22_27"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_22_27" result="shape"/>
</filter>
<linearGradient id="paint0_linear_22_27" x1="0.953613" y1="1.30035" x2="0.953613" y2="5.74479" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F0F0F0"/>
</linearGradient>
<clipPath id="clip0_22_27">
<rect width="20.2222" height="14" fill="white"/>
</clipPath>
</defs>
</svg>
