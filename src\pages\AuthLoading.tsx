import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "react-oidc-context";
import config from "../config";

const AuthLoading: React.FC = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [authTimeout, setAuthTimeout] = useState<ReturnType<typeof setTimeout> | null>(null);

  // Set a timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isLoadingData) {
        console.log("⚠️ Authentication timeout - redirecting to home");
        setError("Authentication timed out. Please try again.");
        setIsLoadingData(false);
      }
    }, 10000); // 10 seconds timeout
    
    setAuthTimeout(timeout);
    
    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, []);

  const fetchWelcomeData = async () => {
    if (!auth.isAuthenticated || !auth.user) {
      console.log("🔴 User not authenticated yet. Waiting...");
      return false;
    }

    try {
      const token = auth.user.access_token;
      const response = await fetch(config.userService.welcome, {
        method: "GET",
        headers: {
          Accept: "*/*",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`❌ Failed to fetch welcome data: ${response.status}`);
      }

      console.log("✅ Welcome Data Fetched Successfully");
      return true;
    } catch (error) {
      console.error("❌ Error during welcome data fetch:", error);
      return false;
    }
  };

  const checkAccess = async () => {
    if (!auth.isAuthenticated || !auth.user) {
      console.log("🔴 User not authenticated yet. Waiting...");
      return;
    }

    try {
      const token = auth.user.access_token;
      const response = await fetch(config.userService.accessCheck, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "*/*",
        },
      });

      if (!response.ok) {
        throw new Error(`❌ Access check failed: ${response.status}`);
      }

      const data = await response.json();

      // Clear timeout as we've successfully completed the auth process
      if (authTimeout) {
        clearTimeout(authTimeout);
        setAuthTimeout(null);
      }
      
      setIsLoadingData(false);

      if (!data.hasAgreedToAgreement) {
        console.log("🔄 Redirecting to user agreement");
        navigate("/user-agreement", { replace: true });
      } else if (!data.org_details_saved) {
        console.log("🔄 Redirecting to organization details");
        navigate("/org", { replace: true });
      } else if (!data.accessGranted) {
        console.log("🔄 Redirecting to payment");
        navigate("/payment", { replace: true });
      } else {
        console.log("🔄 Redirecting to dashboard");
        navigate("/dashboard", { replace: true });
      }
    } catch (error) {
      console.error("❌ Access check failed:", error);
      setError("Failed to check access. Please try again.");
      setIsLoadingData(false);
    }
  };

  useEffect(() => {
    // Detect if we're coming from browser back button
    const isBackNavigation = performance && 
                            performance.getEntriesByType &&
                            performance.getEntriesByType("navigation").length > 0 &&
                            (performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming).type === 'back_forward';
    
    // If this is a back navigation and we're not in an auth flow, redirect to home
    if (isBackNavigation && !window.location.search.includes('code=')) {
      console.log("🔄 Detected back navigation outside auth flow, redirecting to home");
      window.location.replace('/');
      return;
    }
    
    const handleAuthentication = async () => {
      const isLoggingOut = sessionStorage.getItem("loggingOut") === "true";
      if (isLoggingOut) {
        console.log("🔄 Logout in progress, redirecting to home...");
        sessionStorage.removeItem("loggingOut");
        window.location.replace("/");
        return;
      }

      if (auth.isLoading) {
        console.log("🔄 Auth is still loading...");

        // ⚠️ Handle stuck auth loading without code in URL
        if (!window.location.search.includes("code=")) {
          console.log("⚠️ Auth stuck loading without code. Clearing OIDC state and redirecting.");
          try {
            await auth.removeUser();
            await auth.clearStaleState?.();
          } catch (e) {
            console.error("Failed to clear OIDC state", e);
          }
          setIsLoadingData(false);
          window.location.replace("/");
        }

        return;
      }

      if (window.location.search.includes("code=") && !auth.isAuthenticated) {
        console.log("🕒 Still processing OIDC redirect callback...");
        return;
      }

      if (auth.isAuthenticated && auth.user) {
        console.log("✅ User is authenticated, proceeding with API calls");

        const welcomeSuccess = await fetchWelcomeData();
        if (welcomeSuccess) {
          await checkAccess();
        } else {
          setError("Failed to fetch welcome data. Please try again.");
          setIsLoadingData(false);
        }
        return;
      }

      if (!auth.isAuthenticated && !window.location.search.includes("code=")) {
        console.log("🔄 Not authenticated, attempting silent sign-in");
        try {
          await auth.signinSilent();
        } catch (e) {
          console.error("❌ Silent sign-in failed, redirecting to home page");
          window.location.replace("/");
        }
        return;
      }

      if (!auth.isAuthenticated) {
        console.log("❌ User not authenticated. Redirecting to home.");
        window.location.replace("/");
      }
    };

    handleAuthentication();
  }, [auth.isLoading, auth.isAuthenticated, auth.user]);

  if (isLoadingData) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
        <p className="mt-4 text-lg text-gray-700">Authenticating... Please wait</p>
       
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-red-500 text-xl">⚠️ {error}</div>
        <button
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
          onClick={() => (window.location.href = "/assessment")}
        >
          Return to Home
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <p className="text-lg text-gray-700">Redirecting...</p>
    </div>
  );
};

export default AuthLoading;
