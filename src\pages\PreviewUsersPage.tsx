import React, { useState } from 'react';
import DefaultLayout from '../layout/DefaultLayout';

interface AzureConnector {
  clientId: string;
  secret: string;
  tenantId: string;
}

const PreviewUsersPage: React.FC = () => {
  const [formData, setFormData] = useState<AzureConnector>({
    clientId: '',
    secret: '',
    tenantId: '',
  });

  const [users, setUsers] = useState<any[]>([]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handlePreview = () => {
    setUsers([
      { id: 1, name: '<PERSON>', email: '<EMAIL>' },
      { id: 2, name: '<PERSON><PERSON>', email: '<EMAIL>' },
    ]);
  };

  return (
    <DefaultLayout>
      <div className="p-6">
      <div className="border p-4 rounded-md shadow-sm mb-6">
        <h2 className="font-semibold mb-4">Preview User : Azure Connector</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Client-ID</label>
            <input
              type="text"
              name="clientId"
              value={formData.clientId}
              onChange={handleChange}
              className="w-full border rounded px-2 py-1 focus:outline-none focus:ring"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Secret</label>
            <input
              type="text"
              name="secret"
              value={formData.secret}
              onChange={handleChange}
              className="w-full border rounded px-2 py-1 focus:outline-none focus:ring"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Tenant-ID</label>
            <input
              type="text"
              name="tenantId"
              value={formData.tenantId}
              onChange={handleChange}
              className="w-full border rounded px-2 py-1 focus:outline-none focus:ring"
            />
          </div>
        </div>
        <button
          onClick={handlePreview}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Preview Users
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full table-auto border-collapse border">
          <thead className="bg-blue-800 text-white">
            <tr>
              <th className="p-2 border">ID</th>
              <th className="p-2 border">Name</th>
              <th className="p-2 border">Email</th>
            </tr>
          </thead>
          <tbody>
            {users.length === 0 ? (
              <tr>
                <td colSpan={3} className="text-center py-4 text-gray-500">
                  No data available. Click "Preview Users" to load.
                </td>
              </tr>
            ) : (
              users.map((user) => (
                <tr key={user.id} className="border-b">
                  <td className="p-2 border">{user.id}</td>
                  <td className="p-2 border">{user.name}</td>
                  <td className="p-2 border">{user.email}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
        </div>
    </DefaultLayout>
  );
};

export default PreviewUsersPage;