import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

const ProtectedRoute: React.FC<{ children: JSX.Element }> = ({ children }) => {
  // Read auth state from localStorage and initialize a local state.
  // Assuming authState is a string (e.g., a token or "true"/"false" flag).
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(
    Boolean(localStorage.getItem('authState'))
  );

  const location = useLocation();

  // Sync across tabs: listen for changes to authState in localStorage.
  useEffect(() => {
    const handleStorage = (event: StorageEvent) => {
      if (event.key === 'authState') {
        // Update isAuthenticated if authState changes in another tab
        setIsAuthenticated(Boolean(event.newValue));
      }
    };
    window.addEventListener('storage', handleStorage);
    return () => {
      window.removeEventListener('storage', handleStorage);
    };
  }, []);

  if (!isAuthenticated) {
    // User not authenticated: save the target route and redirect to login.
    localStorage.setItem('lastVisitedPage', location.pathname + location.search);
    return <Navigate to="/assessment/" replace />;  // redirect to login page
  }

  // (Optional) If the user is authenticated and they navigate to the login page,
  // redirect them to their last visited page or a default protected page.
  // This prevents an already-logged-in user from seeing the login screen.
  if (location.pathname === '/assessment/' || location.pathname === '/assessment') {
    const lastPage = localStorage.getItem('lastVisitedPage');
    if (lastPage) {
      localStorage.removeItem('lastVisitedPage');       // use it once, then clear
      return <Navigate to={lastPage} replace />;        // go to the intended page
    }
    // If no last page saved, you could redirect to a default homepage, e.g.:
    // return <Navigate to="/dashboard" replace />;
  }

  // User is authenticated and not on the login page: allow access to the protected component.
  return children;
};

export default ProtectedRoute;
