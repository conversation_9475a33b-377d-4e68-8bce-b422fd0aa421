.top-heading {
  color: #002574;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.progress-text {
  color: #fff;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.paper-bg {
  background-color: #002574 !important;
}

.btn {
  background-color: #00c1a0 !important;
  font-weight: bold;
  text-transform: none;
  padding: 12px 24px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
}

.btn:hover {
  background-color: #009b80 !important;
}

.d-r-heading {
  color: #18f1cc;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.d-r-text {
  color: #fff;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.q-heading {
  color: #002574;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.answer-button {
  background: #5eca98;
  color: #fff;
  border: none;
  cursor: pointer;
}

.answer-button:hover {
  background: #4ebf8a; /* Slightly darker shade for hover */
}

.not-answered {
  background: #fe5c77 !important; /* Red background if not answered */
}

.not-answered:hover {
  background: #e94a65 !important; /* Darker red on hover */
}

.q-text {
  color: #000;
  font-style: normal;
  font-weight: 400;
}
