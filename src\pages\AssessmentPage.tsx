import React, { useState, useEffect, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import DefaultLayout from '../layout/DefaultLayout';
import {
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  LinearProgress,
  Button,
} from '@mui/material';
import Questions from './Questions';
import { useAuth } from 'react-oidc-context';
import config from '../config';
import { useNavigate } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify'; // Import toaster library
import 'react-toastify/dist/ReactToastify.css'; // Toaster styles
import './AssessmentPage.css';
import CloseIcon from '@mui/icons-material/Close';


const domainShortCodeMap: Record<string, string> = {
  'Access Control': 'AC',
  'Identification & Authentication': 'IA',
  'Media Protection': 'MP',
  'Physical Protection': 'PE',
  'System & Communications Protection': 'SC',
  'System & Information Integrity': 'SI',
};


const AssessmentPage: React.FC = () => {
  const { domain } = useParams<{ domain?: string }>();
  const auth = useAuth();
  const [refreshKey, setRefreshKey] = useState(0);
  const [authLoading, setAuthLoading] = useState(true); // Added auth loading state
  const [domainTitle, setDomainTitle] = useState<string>('');
  const [recommendation, setrecommendation] = useState<string>('');
  const [practices, setPractices] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [questionsLoading, setQuestionsLoading] = useState<boolean>(false);
  const [questions, setQuestions] = useState<any[]>([]);
  const [responses, setResponses] = useState<any[]>([]);
  const [activeDomain, setActiveDomain] = useState<string | null>(
    domain || null,
  ); // Track active domain
  const [documentationProvided, setDocumentationProvided] = useState<{
    [key: string]: boolean;
  }>({});
  const [activePracticeProgress, setActivePracticeProgress] =
    useState<number>(0);
  const navigate = useNavigate();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [domains, setDomains] = useState<any[]>([]);
const introAudios = [
  "https://cmmc-audio.s3.us-east-1.amazonaws.com/CMMC+Level+1+Access+Control+Q%26A.mp3",
  "https://cmmc-audio.s3.us-east-1.amazonaws.com/CMMC+Level+1_+Identification+and+Authentication+Explained.mp3",
  "https://cmmc-audio.s3.us-east-1.amazonaws.com/CMMC+Level+1_+Media+Protection+Q%26A+(1).mp3",
  "https://cmmc-audio.s3.us-east-1.amazonaws.com/CMMC+Level+1_+Physical+Protection+Q%26A.mp3",
  "https://cmmc-audio.s3.us-east-1.amazonaws.com/CMMC+Level+1_+System+And+Communication.mp3",
  "https://cmmc-audio.s3.us-east-1.amazonaws.com/CMMC+Level+1_+System+Integrity+Q%26A.mp3",
];


  const currentAudio = useMemo(() => {
    const index = domains.findIndex((d) => d.domainName === domainTitle);
    return introAudios[index] || introAudios[0];
  }, [domains, domainTitle]);

  const cleanedDomainTitle = useMemo(() => domainTitle.split(' (')[0], [domainTitle]);
const shortCode = domainShortCodeMap[cleanedDomainTitle] || cleanedDomainTitle;


const handleDomainPdf = (domainTitle: string) => {
  const cleanedTitle = domainTitle.split(' (')[0]; // Remove any suffix in parentheses
  const shortCode = domainShortCodeMap[cleanedTitle];

  if (!shortCode) {
    toast.error(`Checklist not available for "${domainTitle}"`);
    return;
  }

  const pdfPath = `/pdfs/${shortCode}-checklist_v1.0.pdf`;

  const link = document.createElement('a');
  link.href = pdfPath;
  link.setAttribute('download', `${shortCode}-checklist_v1.0.pdf`);
  document.body.appendChild(link);
  link.click();
  link.remove();

  toast.success(`${shortCode} checklist PDF downloaded!`);
};






  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };
  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      const token = auth.user.access_token;
      // Optional: Check if the token has expired
      if (isTokenExpired(token)) {
        console.warn('Token has expired. Redirecting to login.');
        auth.signinRedirect(); // Redirect to login
        return null;
      }
      return token;
    }
    console.warn('User is not authenticated. Cannot fetch token.');
    return null;
  };
  const isTokenExpired = (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT token payload
      return payload.exp * 1000 < Date.now(); // Compare expiry with current time
    } catch (error) {
      console.error('Error decoding token:', error);
      return true;
    }
  };

  useEffect(() => {
    const checkAccess = async () => {
      if (!auth.isAuthenticated || !auth.user) {
        console.error('User is not authenticated yet.');
        return;
      }

      try {
        const token = await getAccessToken();
        if (!token) {
          return;
        }

        const response = await fetch(`${config.userService.accessCheck}`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            accept: '*/*',
          },
        });

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json(); // ✅ Parse JSON response

        // ✅ Explicitly check if accessGranted is false
        if (!data.hasAgreedToAgreement) {
          navigate('/user-agreement', { replace: true });
        } else if (!data.org_details_saved) {
          navigate('/org', { replace: true });
        } else if (!data.accessGranted) {
          navigate('/payment', { replace: true });
        }
      } catch (error) {
        console.error('Access check failed:', error);
      }
    };

    if (auth.isAuthenticated && auth.user) {
      checkAccess();
    }
  }, [auth.isAuthenticated, auth.user, navigate]);

  useEffect(() => {
    setLoading(false);
    // Wait for authentication context to be ready
    if (!auth.isLoading) {
      setAuthLoading(false);
    }
  }, [auth.isLoading]);
  const fetchDomains = async () => {
    try {
      const token = await getAccessToken();
      if (!token) return;
      const response = await axios.get(
        `${config.userService.domainsProgress}`,
        {
          headers: {
            accept: 'application/json',
            Authorization: `Bearer ${token}`,
          },
        },
      );
      setDomains(response.data || []);
    } catch (error) {
      console.error('Error fetching domains:', error);
    }
  };
  useEffect(() => {
    fetchDomains();
  }, []);
  const safeDomain = domain || 'default-domain';
  const fetchDomainPractices = async (isRefresh: boolean = false) => {
    // if (!isRefresh) setLoading(true);  // Only set loading to true if it's not a refresh
    try {
      const token = await getAccessToken();
      if (!token) return;
      const response = await axios.get(
        `${config.userService.domainsPractices}/${encodeURIComponent(
          safeDomain,
        )}/practices`,
        {
          headers: {
            accept: '*/*',
            Authorization: `Bearer ${token}`,
          },
        },
      );
      setDomainTitle(response.data.domainTitle || 'Unknown Domain');
      setPractices(response.data.practices || []);
      if (response.data.practices.length > 0) {
        setActiveTab(
          (prevTab) => prevTab || response.data.practices[0].practice,
        );
      }
    } catch (error) {
      console.error('Error fetching domain practices:', error);
    } finally {
      // if (!isRefresh) setLoading(false); // Only set loading to false if it's not a refresh
    }
  };
  useEffect(() => {
    setLoading(false);
    if (authLoading) return; // Wait until auth is ready
    fetchDomainPractices();
  }, [authLoading, safeDomain]);
  useEffect(() => {
    setLoading(false);
    const currentPractice = practices.find(
      (practice) => practice.practice === activeTab,
    );
    setActivePracticeProgress(currentPractice?.progress || 0);
    setrecommendation(currentPractice?.recommendation || ''); // Update recommendation state based on active tab
  }, [activeTab, practices]);

  useEffect(() => {
    if (practices.length > 0 && !activeTab) {
      setActiveTab(practices[0].practice);
    }
  }, [practices]);

  const fetchQuestionsAndProgress = async () => {
    if (!activeTab) return;
    setQuestionsLoading(true);
    try {
      const token = await getAccessToken();
      if (!token) return;

      const questionsResponse = await axios.get(
        `${config.userService.domainsQuestions}/${encodeURIComponent(
          safeDomain,
        )}/practice/${encodeURIComponent(activeTab)}`,
        {
          headers: {
            accept: 'application/json',
            Authorization: `Bearer ${token}`,
          },
        },
      );
      const fetchedQuestions = questionsResponse.data.content || [];

      const progressResponse = await axios.get(
        `${
          config.userService.questionsProgress
        }?domainTitle=${encodeURIComponent(
          domainTitle,
        )}&practice=${encodeURIComponent(activeTab)}`,
        {
          headers: {
            accept: '*/*',
            Authorization: `Bearer ${token}`,
          },
        },
      );

      const progressData = Array.isArray(progressResponse.data)
        ? progressResponse.data
        : [];

      setResponses(progressData); // ✅ Store the API response in state

      const questionsWithProgress = fetchedQuestions.map((question: any) => {
        const matchingProgress = progressData.find(
          (progress: any) => progress.questionId === question.questionId,
        );
        return {
          id: question.questionId,
          questionText: question.questionText,
          questionOrder: question.questionOrder, // ✅ Fix: Ensure this is included
          progress: matchingProgress
            ? parseInt(matchingProgress.answer, 10)
            : null, // ✅ Store `null` for unanswered questions
          notes: matchingProgress ? matchingProgress.notes : '',
          documentationProvided: matchingProgress
            ? matchingProgress.documentationProvided
            : false,
        };
      });

      setQuestions(questionsWithProgress);
    } catch (error) {
      console.error('Error fetching questions and progress:', error);
    } finally {
      setQuestionsLoading(false);
    }
  };

  useEffect(() => {
    setLoading(false);
    if (authLoading || !activeTab) return;
    fetchQuestionsAndProgress();
  }, [activeTab, safeDomain, domainTitle, authLoading]);
  useEffect(() => {
    setLoading(false);
    if (authLoading || !activeTab) return; // Ensure authLoading is complete and activeTab is set
    fetchQuestionsAndProgress();
  }, [activeTab, safeDomain, domainTitle, authLoading]);

  const handleDownloadReport = async () => {
    try {
      const token = await getAccessToken();
      if (!token) return;

      const response = await axios.get(`${config.userService.reportPdf}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          accept: '*/*',
        },
        responseType: 'blob',
      });

      // Get current date and time in YYYY-MM-DD_HH-MM-SS format
      const now = new Date();
      const formattedDate = `${now.getFullYear()}-${String(
        now.getMonth() + 1,
      ).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}_${String(
        now.getHours(),
      ).padStart(2, '0')}-${String(now.getMinutes()).padStart(2, '0')}-${String(
        now.getSeconds(),
      ).padStart(2, '0')}`;

      // Create a download link for the PDF
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Assessment_Report_${formattedDate}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.parentNode?.removeChild(link);

      // ✅ Show success toast
      toast.success('Report downloaded successfully!');
    } catch (error) {
      console.error('Error downloading report:', error);

      // ❌ Show error toast
      toast.error('Failed to download report. Please try again.');
    }
  };

  const handleHeaderRefresh = () => {
    setRefreshKey((prev) => prev + 1); // 🔄 Increment refreshKey to trigger Header reload
  };

  return (
    <DefaultLayout refreshKey={refreshKey}>
      <ToastContainer
        position="top-center" // Use "bottom-center" for bottom-middle placement
        autoClose={3000} // Auto close after 3 seconds
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      <Box sx={{ bgcolor: 'white', minHeight: '100vh', px: 3, py: 4 }}>
        {loading ? (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            height="200px"
          >
            <CircularProgress />
          </Box>
        ) : (
          <>
            {/* Hamburger Icon and Sidebar */}

            {/* Hamburger Icon and Sidebar */}
            <Box display="flex" alignItems="center" mb={2}>
              <IconButton
                edge="start"
                color="inherit"
                aria-label="menu"
                onClick={toggleSidebar}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
              <Typography className="top-heading" variant="h6">
                Assessment for {domainTitle}
              </Typography>
            </Box>
            <Box mb={2} mt={1}>
        <Typography variant="subtitle1" className="text-sm font-medium mb-1 text-blue-800">
  🎧 Click to understand {shortCode} domain
</Typography>



              <audio
                controls
                src={currentAudio}
                className="w-full rounded-md"
                controlsList="nodownload"
                onContextMenu={(e) => e.preventDefault()}
              >
                Your browser does not support the audio element.
              </audio>
            </Box>

            <Box display="flex" justifyContent="flex-end" mb={3}>
<Button
  variant="outlined"
  size="small"
  onClick={() => handleDomainPdf(cleanedDomainTitle)}
  sx={{
    textTransform: 'none',
    fontWeight: 'bold',
    fontSize: '0.875rem',
    color: '#002574',
    borderColor: '#002574',
    '&:hover': {
      backgroundColor: '#002574',
      color: 'white',
    },
  }}
>
  Download recommended checklist for {shortCode}
</Button>




</Box>


            <Drawer anchor="left" open={isSidebarOpen} onClose={toggleSidebar}>
              <Box
                sx={{
                  width: { xs: '100vw', sm: '80vw', md: '60vw', lg: '25vw' }, // ✅ Adjusted dynamically
                  backgroundColor: '#fff',
                  height: '100vh',
                  boxShadow: '0 0.25rem 0.625rem rgba(0, 0, 0, 0.1)',
                  overflowY: 'auto',
                  overflowX: 'hidden', // ✅ Prevents horizontal scrolling
                  padding: '1rem 0',
                  transition: 'all 0.3s ease-in-out',

                  // ✅ Scrollbar Fix
                  '&::-webkit-scrollbar': { width: '0.4rem' },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: '#00C1A0',
                    borderRadius: '1rem',
                  },
                  '&::-webkit-scrollbar-track': {
                    backgroundColor: 'transparent',
                  },
                }}
              >
                {/* Close Icon in Sidebar */}
                <Box
                  display="flex"
                  justifyContent="flex-end"
                  pr="1.5rem"
                  pb="1rem"
                >
                  <IconButton onClick={toggleSidebar}>
                    <CloseIcon sx={{ fontSize: '1.2em', color: '#002574' }} />
                  </IconButton>
                </Box>

                <List>
                  {domains.map((domain) => (
                    <ListItem
                      key={domain.domainName}
                      sx={{
                        margin: '0.5vh 0',
                        borderRadius: '0.5rem',
                        transition: 'all 0.3s ease-in-out',
                        '&:hover': {
                          transform: 'scale(1.02)', // ✅ Subtle zoom effect
                        },
                      }}
                    >
                      <ListItemButton
                        onClick={() => {
                          navigate(
                            `/domains/${encodeURIComponent(domain.domainName)}`,
                          );
                          setDomainTitle(domain.domainName);
                          setIsSidebarOpen(false);
                        }}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          textAlign: 'left',
                          width: '100%',
                          padding: '0.75rem 1rem',
                          border: 'none',
                          background: 'transparent',
                          cursor: 'pointer',
                          fontWeight: 'bold',
                          fontSize: '1rem',
                          borderRadius: '0.5rem',
                          position: 'relative',
                          transition: 'all 0.3s ease-in-out',

                          // ✅ Active Item Styling
                          backgroundColor:
                            activeTab === domain.domainName
                              ? '#00C1A0'
                              : 'transparent',
                          color:
                            activeTab === domain.domainName
                              ? '#fff'
                              : '#002574',
                          boxShadow:
                            activeTab === domain.domainName
                              ? '0 0.25rem 0.625rem rgba(0, 0, 0, 0.15)'
                              : 'none',

                          // ✅ Hover Effects
                          '&:hover': {
                            background:
                              activeTab === domain.domainName
                                ? '#00A080'
                                : 'rgba(0, 193, 160, 0.15)', // ✅ Lighter Green Hover
                            color: '#002574',
                            boxShadow: '0 0.2rem 0.4rem rgba(0, 0, 0, 0.1)', // ✅ Soft shadow effect
                          },
                        }}
                      >
                        <ListItemText
                          primary={domain.domainName}
                          primaryTypographyProps={{
                            fontWeight: 'bold',
                            fontSize: '1rem',
                            color: 'inherit',
                          }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              </Box>
            </Drawer>

            {/* Main Content */}
            <Paper className="paper-bg" sx={{ p: 3, mb: 4 }}>
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                flexWrap="wrap"
                gap={2}
                textAlign="center"
              >
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={() => navigate('/dashboard')}
                  className="btn font-bold text-xs sm:text-sm md:text-base lg:text-lg px-3 py-1.5 shadow-md mr-2"
                >
                  Dashboard
                </Button>
                <p className="progress-text">
                  Assessment Progress for {domainTitle} - Practice {activeTab}
                </p>

                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleDownloadReport}
                  className="btn normal-case flex items-center"
                >
                  <i className="material-icons"></i>
                  Download Report
                </Button>
              </Box>
              <Box display="flex" alignItems="center" gap={2} mt={2}>
                <LinearProgress
                  variant="determinate"
                  value={activePracticeProgress}
                  sx={{
                    flex: 1,
                    height: '8px',
                    borderRadius: '4px',
                    bgcolor: '#1745A6', // Rest color (background of the progress bar)
                    '& .MuiLinearProgress-bar': { bgcolor: '#0EF2CB' }, // Active progress color
                  }}
                />
                <p className="progress-text">{activePracticeProgress}%</p>
              </Box>
            </Paper>
            <Box>
              {/* Instructions for the user */}
              <p className="mt-2 text-gray-700 text-sm sm:text-base md:text-lg mb-2">
                Click on the tabs below to switch between different assessment
                sections based on practices. The active selection will be
                highlighted in green and will display the number of answered and
                total questions.
              </p>

              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 4 }}>
                {practices.map((practice) => {
                  // 🔹 Find all questions related to this practice
                  const practiceQuestions = questions.filter((q) =>
                    responses.some((res) => res.questionId === q.id),
                  );

                  const totalQuestions = practice.questionCount || 0;

                  // 🔹 Count answered questions by checking valid responses
                  const answeredCount = questionsLoading
                    ? 'Loading...'
                    : String(
                        responses.filter(
                          (response) =>
                            practiceQuestions.some(
                              (question) => question.id === response.questionId,
                            ) && response.answer !== null,
                        ).length,
                      );

                  return (
                    <button
                      key={practice.practiceId}
                      className={`px-4 py-2 rounded-lg font-semibold ${
                        activeTab === practice.practice
                          ? 'bg-green-500 text-white'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      }`}
                      onClick={() => {
                        if (activeTab !== practice.practice) {
                          setResponses([]); // ✅ Clear responses ONLY if switching to a new practice
                          setActiveTab(practice.practice);
                        }
                      }}
                    >
                      {practice.practice}
                      {activeTab === practice.practice &&
                        ` (${answeredCount}/${totalQuestions} Answered)`}
                    </button>
                  );
                })}
              </Box>
            </Box>

            <Box className="paper-bg mt-4 p-4 border border-gray-300 rounded-lg md:p-6 lg:p-8">
              <Typography
                variant="h6"
                className="d-r-heading text-lg font-semibold  mb-4"
              >
                Description
              </Typography>
              <Typography variant="body1" className="d-r-text ">
                {practices.find((practice) => practice.practice === activeTab)
                  ?.description || 'No description available.'}
              </Typography>
            </Box>
            <Box className="paper-bg mt-4 p-4 border border-gray-300 rounded-lg md:p-6 lg:p-8">
              <Typography
                variant="h6"
                className="d-r-heading text-lg font-semibold mb-4"
              >
                Recommendations
              </Typography>
              {recommendation ? (
                <ul className="list-disc pl-6 space-y-2 d-r-text">
                  {recommendation
                    .split('.')
                    .map((sentence, index) =>
                      sentence.trim() ? (
                        <li key={index}>{sentence.trim()}.</li>
                      ) : null,
                    )}
                </ul>
              ) : (
                <Typography variant="body1" className="text-gray-500">
                  No recommendations available.
                </Typography>
              )}
            </Box>

            {activeTab && (
              <Questions
                refreshHeader={handleHeaderRefresh}
                questions={questions}
                responses={responses} // ✅ Pass responses as a prop
                onProgressChange={(id, value) => {
                  setQuestions((prev) =>
                    prev.map((q) =>
                      q.id === id ? { ...q, progress: value } : q,
                    ),
                  );
                }}
                onDocumentationToggle={(id) => {
                  setDocumentationProvided((prev) => ({
                    ...prev,
                    [id]: !prev[id],
                  }));
                }}
                documentationProvided={documentationProvided}
                domainTitle={domainTitle}
                practice={activeTab}
                refreshData={() => {
                  fetchQuestionsAndProgress();
                  fetchDomainPractices(true);
                }}
                questionsLoading={questionsLoading}
              />
            )}
            <p className="mt-2 text-gray-700 text-sm sm:text-base md:text-lg mb-2">
              Click on the tabs below to switch between different assessment
              sections based on practices. The active selection will be
              highlighted in green and will display the number of answered and
              total questions.
            </p>

            <Box>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 4 }}>
                {practices.map((practice) => {
                  // 🔹 Find all questions related to this practice
                  const practiceQuestions = questions.filter((q) =>
                    responses.some((res) => res.questionId === q.id),
                  );

                  const totalQuestions = practice.questionCount || 0;

                  // 🔹 Count answered questions by checking valid responses
                  const answeredCount = questionsLoading
                    ? 'Loading...'
                    : String(
                        responses.filter(
                          (response) =>
                            practiceQuestions.some(
                              (question) => question.id === response.questionId,
                            ) && response.answer !== null,
                        ).length,
                      );

                  return (
                    <button
                      key={practice.practiceId}
                      className={`px-4 py-2 rounded-lg font-semibold ${
                        activeTab === practice.practice
                          ? 'bg-green-500 text-white'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      }`}
                      onClick={() => {
                        if (activeTab !== practice.practice) {
                          setResponses([]); // ✅ Clear responses ONLY if switching to a new practice
                          setActiveTab(practice.practice);
                        }
                      }}
                    >
                      {practice.practice}
                      {activeTab === practice.practice &&
                        ` (${answeredCount}/${totalQuestions} Answered)`}
                    </button>
                  );
                })}
              </Box>
            </Box>
          </>
        )}
      </Box>

      <div className="w-full bg-white shadow-md mt-6 py-3 border-t border-gray-300">
        {/* Instructions */}
        <div className="text-center text-gray-600 text-sm sm:text-base mb-2 px-4">
          <p>
            <strong>Navigation Instructions:</strong> Click on a domain below to
            switch between different assessment categories.
          </p>
          <p>
            The selected domain will be highlighted, and you will be redirected
            accordingly.
          </p>
        </div>

        {/* Navigation Menu */}
        <nav className="flex flex-wrap justify-center items-center gap-6 sm:gap-8 md:gap-10">
          {domains.map((d, index) => (
            <button
              key={d.domainName}
              onClick={async () => {
                setActiveDomain(d.domainName);
                navigate(`/domains/${encodeURIComponent(d.domainName)}`);

                // Fetch practices for selected domain
                try {
                  const token = await getAccessToken();
                  if (!token) return;

                  const response = await axios.get(
                    `${
                      config.userService.domainsPractices
                    }/${encodeURIComponent(d.domainName)}/practices`,
                    {
                      headers: {
                        accept: 'application/json',
                        Authorization: `Bearer ${token}`,
                      },
                    },
                  );

                  const fetchedPractices = response.data.practices || [];
                  setPractices(fetchedPractices);

                  // ✅ Automatically set the first practice as activeTab
                  if (fetchedPractices.length > 0) {
                    setActiveTab(fetchedPractices[0].practice);
                  } else {
                    setActiveTab('');
                  }
                } catch (error) {
                  console.error('Error fetching practices:', error);
                }
              }}
              className={`text-sm sm:text-base md:text-lg font-semibold border-b-2 pb-1 transition-all duration-300 ${
                activeDomain
                  ? activeDomain === d.domainName
                    ? 'text-blue-600 border-blue-600 font-bold'
                    : 'text-blue-500 border-transparent hover:text-blue-600 hover:border-blue-600'
                  : index === 0
                  ? 'text-blue-600 border-blue-600 font-bold' // ✅ Auto-highlight first domain if none is selected
                  : 'text-blue-500 border-transparent hover:text-blue-600 hover:border-blue-600'
              }`}
            >
              {d.domainName}
            </button>
          ))}
        </nav>
      </div>
    </DefaultLayout>
  );
};

export default AssessmentPage;
