import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './QuestionApp.css';
import VerticalProgressBar from './VerticalProgressBar';
import DefaultLayout from '../layout/DefaultLayout';
import InfoIcon from '@mui/icons-material/Info';
import Tooltip from '@mui/material/Tooltip';
import { useAuth } from 'react-oidc-context'; // Ensure this is imported
import config from '../config';

const levelsData: any[] = []; // Initialize empty array

export const QuestionApp: React.FC = () => {
  const [currentLevel, setCurrentLevel] = useState(0);
  const [levels, setLevels] = useState(levelsData);
  const [isLoading, setIsLoading] = useState(true); // Track loading state
  const [provideEvidence, setProvideEvidence] = useState(''); // Track evidence selection
  const auth = useAuth(); // Access the auth context
  const userId = auth.user?.profile?.sub;
  const [progressPercent, setProgressPercent] = useState(null); // State to store the progress percent
  const totalQuestions = levels[currentLevel]?.questions?.length || 0;
  const answeredQuestions =
    levels[currentLevel]?.questions?.filter((q: any) => q.progress > 0)
      .length || 0;
  const unansweredQuestions = totalQuestions - answeredQuestions;
  const [documentationProvided, setDocumentationProvided] = useState<{
    [key: string]: boolean;
  }>({});

  // Helper function to get the latest token
  const getAccessToken = async () => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token; // Use the current token
    }
    console.warn('User is not authenticated. Cannot fetch token.');
    return null;
  };

  const handleCheckboxChange = (questionId: string) => {
    setDocumentationProvided((prevState) => ({
      ...prevState,
      [questionId]: !prevState[questionId], // Toggle the checkbox state
    }));
  };

  useEffect(() => {
    const fetchLevelsAndProgressData = async () => {
      try {
        const token = await getAccessToken();

        if (!token) {
          console.error('No access token found. Please authenticate.');
          return;
        }

        const email = auth.user?.profile?.email;

        if (!email) {
          console.error('Email is missing from user profile.');
          return;
        }

        // Fetch the total number of levels
        const initialResponse = await axios.get(
          `${config.userService.levels}?page=0&size=1&sort=level,asc`,
          {
            headers: {
              accept: '*/*',
              Authorization: `Bearer ${token}`,
            },
          },
        );

        const totalLevels = initialResponse?.data?.totalElements || 0;

        if (totalLevels === 0) {
          console.warn('No levels data available.');
          setLevels([]);
          setIsLoading(false);
          return;
        }

        // Calculate the page size dynamically
        const pageSize = totalLevels;

        // Fetch levels data with dynamic page size and sorted by level
        const levelsResponse = await axios.get(
          `${config.userService.levels}?page=0&size=${pageSize}&sort=level,asc`,
          {
            headers: {
              accept: '*/*',
              Authorization: `Bearer ${token}`,
            },
          },
        );

        // Map response data to levels structure
        const levelsData =
          levelsResponse?.data?.content?.map((domain: any) => ({
            domainId: domain.id,
            questionnaireId: domain.questionnaireId,
            level: domain.level,
            domain: domain.domainTitle,
            capability: domain.capability,
            practice: domain.practice,
            description: domain.description,
            details: domain.details,
            createdAt: domain.createdAt,
            questions: [], // Initialize questions as an empty array
          })) || [];

        // Fetch questions for each domain sorted by questionOrder
        const updatedLevels = await Promise.all(
          levelsData.map(async (level: any) => {
            const questionsResponse = await axios.get(
              `${config.userService.levelQuestions}/${level.domainId}?page=0&size=${pageSize}&sort=questionOrder,asc`,
              {
                headers: {
                  accept: '*/*',
                  Authorization: `Bearer ${token}`,
                },
              },
            );

            const questions =
              questionsResponse?.data?.content?.map((question: any) => ({
                id: question.questionId, // Use questionId from API
                questionText: question.questionText, // Use questionText from API
                progress: 0, // Default progress, updated below
                questionOrder: question.questionOrder, // Use questionOrder from API
              })) || [];

            return {
              ...level,
              questions,
            };
          }),
        );

        // Fetch progress for all levels
        const levelsWithProgress = await Promise.all(
          updatedLevels.map(async (level: any) => {
            if (!level.domainId) {
              return level;
            }

            const progressResponse = await axios.get(
              `${config.userService.progress}/${level.domainId}`,
              {
                headers: {
                  accept: '*/*',
                  Authorization: `Bearer ${token}`,
                },
              },
            );

            const progressData = Array.isArray(progressResponse?.data)
              ? progressResponse.data
              : [];

            // Merge progress data into questions
            const questionsWithProgress = level.questions.map(
              (question: any) => {
                const matchingResponse = progressData.find(
                  (res: any) => res.questionId === question.id,
                );
                return {
                  ...question,
                  progress: matchingResponse
                    ? parseInt(matchingResponse.answer, 10)
                    : 0,
                };
              },
            );

            return {
              ...level,
              questions: questionsWithProgress,
            };
          }),
        );

        setLevels(levelsWithProgress);
      } catch (error) {
        console.error('Error fetching levels or progress data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLevelsAndProgressData();
  }, [auth, currentLevel]);

  const getProgressBarStyle = (value: number) => {
    if (value === 0) {
      return '#D9D9D9'; // Solid color for 0 progress
    }

    if (value <= 25) {
      return `linear-gradient(90deg, #FD6238 0%, #FB9B60 ${value}%, #D9D9D9 ${value}%)`;
    } else if (value <= 50) {
      return `linear-gradient(90deg, #FD6238 0%, #FC7F4C 25%, #F8EE8E ${
        value - 25
      }%, #D9D9D9 ${value}%)`;
    } else if (value <= 75) {
      return `linear-gradient(90deg, #FF7A7A 0%, #FFAD6A 32.85%, #F6FF98 ${
        value - 50
      }%, #D9D9D9 ${value}%)`;
    } else {
      return `linear-gradient(90deg, #FF7A7A 0%, #FFAD6A 23.98%, #F6FF98 49.31%, #4CFF7F 74.63%, #21E996 ${
        value - 75
      }%, #D9D9D9 ${value}%)`;
    }
  };

  const handleProgressChange = async (questionId: string, value: number) => {
    // Retrieve the access token for authorization
    const token = await getAccessToken();
    if (!token) {
      console.error('No access token found. Cannot send progress data.');
      return;
    }

    try {
      // Construct the payload with the questionId, progress value, and metadata
      const payload = {
        questionId,
        answer: `${value}`, // Progress value as a string
        documentationProvided: documentationProvided[questionId] || false, // Metadata indicating if documentation is provided
      };

      // Send the payload to the backend API to update progress
      const response = await axios.post(
        config.userService.updateProgress,
        payload,
        {
          headers: {
            accept: '*/*',
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (response.status === 200 || response.status === 201) {
        // Update the progress in the local state if the API call is successful
        setLevels((prevLevels) =>
          prevLevels.map((level, levelIndex) => {
            if (levelIndex === currentLevel) {
              return {
                ...level,
                questions: level.questions.map((question: any) =>
                  question.id === questionId
                    ? { ...question, progress: value }
                    : question,
                ),
              };
            }
            return level;
          }),
        );
      } else {
        console.error(
          `Unexpected response while sending progress data. Status: ${response.status}`,
        );
      }
    } catch (error) {
      // Log errors that occur during the API call
      console.error(
        `Error sending progress data for question ${questionId}:`,
        error,
      );
    }
  };

  const handleNext = () => {
    if (currentLevel < levels.length - 1) {
      setCurrentLevel(currentLevel + 1);
      setProvideEvidence(''); // Reset evidence selection for the next level
    } else {
      alert('You have completed all levels!');
    }
  };

  if (isLoading) {
    return <div>Loading...</div>; // Render a loading state while data is being fetched
  }

  return (
    <DefaultLayout>
      <div className="min-h-screen bg-gray-100">
        <div className="flex flex-col md:flex-row">
          {/* Left Section */}
          <div className="w-full md:w-1/4 bg-blue-900 p-4 shadow-md flex flex-col gap-4">
            <div className="flex justify-between ">
              <span className="big-d text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold">
                {levels[currentLevel].level}
              </span>

              <p className="h-white">
                Practice {levels[currentLevel].level}
                <span className="of-d"> of 17 </span>
                <Tooltip title={levels[currentLevel].level} arrow>
                  <InfoIcon
                    className="ml-2 cursor-pointer text-gray-600 hover:text-blue-500"
                    fontSize="small"
                  />
                </Tooltip>
              </p>

              <p></p>
            </div>
            <div className="flex justify-between  text-white rounded-lg">
              <div className="text-center">
                <p className="l-flex mb-3">Total Questions</p>
                <p className="l-f-b">{totalQuestions}</p>
              </div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="2"
                height="48"
                viewBox="0 0 2 48"
                fill="none"
              >
                <path d="M1 0V48" stroke="white" stroke-width="0.25" />
              </svg>
              <div className="text-center">
                <p className="l-flex mb-3">Answered</p>
                <p className="l-f-b">{answeredQuestions}</p>
              </div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="2"
                height="48"
                viewBox="0 0 2 48"
                fill="none"
              >
                <path d="M1 0V48" stroke="white" stroke-width="0.25" />
              </svg>
              <div className="text-center">
                <p className="l-flex mb-3">Unanswered</p>
                <p className="l-f-b">{unansweredQuestions}</p>
              </div>
            </div>

            <div className="flex flex-col">
              <span className="l-g mb-2">Domain</span>
              <h4 className="l-h font-bold text-white mb-4">
                {levels[currentLevel].domain}
              </h4>
            </div>

            <p className="flex flex-col">
              <span className="l-g mb-2"> Capability: </span>

              <span className="l-h font-normal text-white">
                {levels[currentLevel].capability}
              </span>
            </p>
            <p className="flex flex-col">
              <span className="l-g mb-2"> Practice: </span>

              <span className="l-h font-normal">
                {levels[currentLevel].practice}
              </span>
            </p>
            <p className="flex flex-col">
              <span className="l-g mb-2"> Description of Control:</span>

              <span className="l-p">{levels[currentLevel].description}</span>
            </p>

            <div className="contact flex flex-col items-center justify-center mx-auto mt-4">
              <span className="l-p text-white font-medium text-center mb-2">
                Need help or facing any issues?
              </span>
              <button
                className="btn-contact text-blue-900 font-bold flex items-center justify-center"
                onClick={() =>
                  (window.location.href = 'mailto:<EMAIL>')
                }
              >
                <span>Contact Us</span>
              </button>
            </div>
          </div>

          {/* Vertical Progress Bar */}
          <div className="mt-2 ml-6">
            <VerticalProgressBar questions={levels[currentLevel].questions} />
          </div>

          {/* Main Content Area */}
          <div className="flex-1 p-8">
            {/* Questions Area */}
            <div>
              {levels?.[currentLevel]?.questions?.length > 0 ? (
                levels[currentLevel].questions.map((question: any) => (
                  <div
                    key={question.id}
                    className="mb-4 p-4 rounded-lg bg-white shadow-md flex flex-col gap-2"
                  >
                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                      <h6 className="q-text flex-2">
                        {question.questionText || 'No question text available'}{' '}
                        {/* Fallback for safety */}
                        <Tooltip
                          title={question.description || 'Additional info'}
                          arrow
                        >
                          <InfoIcon
                            className="ml-2 cursor-pointer"
                            fontSize="small"
                            style={{ color: '#00C1A0' }}
                          />
                        </Tooltip>
                      </h6>
                      {/* Slider and Progress */}
                      <div className="flex items-center gap-4 w-full sm:w-auto">
                        <input
                          type="range"
                          min={0}
                          max={100}
                          step={25}
                          value={question.progress}
                          onChange={(e) =>
                            handleProgressChange(
                              question.id,
                              parseInt(e.target.value, 10),
                            )
                          }
                          className="flex-1 h-2 rounded-full appearance-none cursor-pointer bg-gray-200"
                          style={{
                            background: getProgressBarStyle(question.progress),
                          }}
                        />
                        <div className="p-b min-w-max">
                          <p>
                            <span className="q-progress">
                              {question.progress}%
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    {/* Documentation Request */}
                    <div className="flex items-center mt-2 ml-auto">
                      <input
                        type="checkbox"
                        className="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        checked={documentationProvided[question.id] || false} // Reflect state
                        onChange={() => handleCheckboxChange(question.id)} // Handle toggle
                      />
                      <span className="doc-text">
                        Can you provide documentation for the above control?
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <p>No questions available for the current level.</p>
              )}
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between mt-4">
              <div>
                <strong className="f-domain">
                  Practice {levels[currentLevel].level}
                </strong>
                <span className="f-of-domain"> of 17</span>
              </div>
              <div className="flex space-x-4">
                <button
                  disabled={currentLevel === 0}
                  onClick={() => setCurrentLevel((prev) => prev - 1)}
                  className={`btn-back flex items-center justify-center gap-2 px-4 py-2 rounded text-white ${
                    currentLevel === 0
                      ? 'bg-gray-300 cursor-not-allowed'
                      : 'bg-green-500 hover:bg-green-600'
                  }`}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="28"
                    height="8"
                    viewBox="0 0 28 8"
                    fill="none"
                  >
                    <path
                      d="M0.646446 4.35355C0.451185 4.15829 0.451185 3.84171 0.646446 3.64645L3.82843 0.464466C4.02369 0.269204 4.34027 0.269204 4.53553 0.464466C4.7308 0.659728 4.7308 0.976311 4.53553 1.17157L1.70711 4L4.53553 6.82843C4.7308 7.02369 4.7308 7.34027 4.53553 7.53553C4.34027 7.7308 4.02369 7.7308 3.82843 7.53553L0.646446 4.35355ZM27.5 4.5H1V3.5H27.5V4.5Z"
                      fill="white"
                    />
                  </svg>
                  <span>Back</span>
                </button>

                <button
                  onClick={handleNext}
                  className="btn-next flex items-center justify-center gap-2 px-4 py-2 rounded text-white hover:bg-blue-600"
                >
                  <span>
                    {currentLevel < levels.length - 1 ? 'Next' : 'Finish'}
                  </span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="27"
                    height="8"
                    viewBox="0 0 27 8"
                    fill="none"
                  >
                    <path
                      d="M26.8536 4.35355C27.0488 4.15829 27.0488 3.84171 26.8536 3.64645L23.6716 0.464466C23.4763 0.269204 23.1597 0.269204 22.9645 0.464466C22.7692 0.659728 22.7692 0.976311 22.9645 1.17157L25.7929 4L22.9645 6.82843C22.7692 7.02369 22.7692 7.34027 22.9645 7.53553C23.1597 7.7308 23.4763 7.7308 23.6716 7.53553L26.8536 4.35355ZM0 4.5H26.5V3.5H0V4.5Z"
                      fill="white"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
};
