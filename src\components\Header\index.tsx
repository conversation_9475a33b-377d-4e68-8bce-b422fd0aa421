import { useState, useEffect } from "react";
import { Box, Typography, LinearProgress } from "@mui/material";
import DropdownUser from "./DropdownUser";
import Logo from "../../images/logo/Logo H.svg";
import ProductLogo from "../../images/logo/CMMCGenie.png";
import "./header.css";
import { useAuth } from "react-oidc-context";
import axios from "axios";
import config from "../../config";
import { useNavigate } from "react-router-dom";

interface HeaderProps {
  refreshKey: number; // ✅ Add refreshKey to trigger re-renders
}

const Header: React.FC<HeaderProps> = ({ refreshKey }) => {
  const [overallProgress, setOverallProgress] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const auth = useAuth();
    const navigate = useNavigate();

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    console.error("User is not authenticated. Cannot fetch token.");
    return null;
  };

  const fetchProgressData = async () => {
    try {
      setLoading(true);
      const token = await getAccessToken();
      if (!token) return;

      // Fetch overall progress
      const overallProgressResponse = await axios.get(
        `${config.userService.domainsOverallProgress}`,
        {
          headers: {
            accept: "*/*",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setOverallProgress(overallProgressResponse.data?.overallProgress || 0);
    } catch (error) {
      console.error("Error fetching progress data:", error);
    } finally {
      setLoading(false);
    }
  };

  // 🔄 Re-fetch progress whenever refreshKey changes
  useEffect(() => {
    fetchProgressData();
  }, [refreshKey]);

   const handleLogoClick = () => {
    navigate('/dashboard');
  };

  return (
    <div>
      <header className="sticky top-0 z-999 flex w-full bg-white drop-shadow-1 dark:bg-boxdark dark:drop-shadow-none">
        <div className="flex flex-grow items-center justify-between px-4 py-4 shadow-2 md:px-6 2xl:px-11">
          {/* Left Side: Logos */}
          <div onClick={handleLogoClick} className="flex flex-col  cursor-pointer"  >
            <img src={Logo} alt="Raaz Security Logo" className="logo mb-2" />
            <img src={ProductLogo} alt="CMMCGenie Logo" className="product-logo" />
          </div>

          {/* Center: Overall Progress Bar */}
          <Box display="flex" alignItems="center" className="hidden sm:flex">
            <Typography variant="body1" sx={{ fontWeight: "bold", mr: 2 }}>
              Overall Progress:
            </Typography>
            <Box width={200}>
              <LinearProgress
                variant="determinate"
                value={overallProgress}
                sx={{
                  height: "8px",
                  borderRadius: "4px",
                  bgcolor: "#e0e0e0",
                  "& .MuiLinearProgress-bar": { bgcolor: "#4caf50" },
                }}
              />
            </Box>
            <Typography variant="body1" sx={{ fontWeight: "bold", ml: 2 }}>
              {Math.round(overallProgress)}%
            </Typography>
          </Box>

          {/* Right Side: User Dropdown */}
          <DropdownUser />
        </div>
      </header>
    </div>
  );
};

export default Header;
