import { useEffect, useState } from 'react';
import { Route, Routes, useLocation } from 'react-router-dom';
import { AuthProvider, useAuth } from 'react-oidc-context';
import Loader from './common/Loader';
import PageTitle from './components/PageTitle';
import SignIn from './pages/Authentication/SignIn';
import ProtectedRoute from './ProtectedRoute';
import { QuestionApp } from './pages/QuestionApp';
import Instructions from './pages/instructions';
import config from './config'; // Import configuration
import Dashboard from './pages/Dashboard';
import AssessmentPage from './pages/AssessmentPage';
import PaymentPage from './pages/PaymentPage';
import UserProfile from './pages/UserProfile';
import WelcomePage from './pages/WelcomePage';
import CompanyForm from './pages/CompanyForm';
import PaymentSuccessPage from './pages/PaymentSuccessPage';
import PaymentFailurePage from './pages/PaymentFailurePage';
import PaymentHistory from './pages/PaymentHistory';
import UserAgreement from './pages/UserAgreement';
import AuthLoading from './pages/AuthLoading';
import SessionTimeoutHandler from './components/SessionTimeoutHandler';
import HistoryHandler from './components/HistoryHandler';
import PreviewUsersPage from './pages/PreviewUsersPage';

const cognitoAuthConfig = {
  authority: config.cognito.authority,
  client_id: config.cognito.clientId,
  client_secret: config.cognito.clientSecret,
  redirect_uri: config.cognito.redirectUri,
  response_type: config.cognito.responseType,
  scope: config.cognito.scope,
  automaticSilentRenew: false,
  loadUserInfo: true,
  monitorSession: true,
  revokeTokensOnSignout: true,
  onSigninCallback: () => {
    // Clear any potential logout flags
    sessionStorage.removeItem('loggingOut');

    // Remove the authorization code from the URL after successful sign in
    window.history.replaceState({}, document.title, window.location.pathname);

    // OIDC library handles auth state, no manual storage needed
    console.log("✅ Sign-in callback completed");
  },
  onSignoutCallback: () => {
    // Clear logout flag
    sessionStorage.removeItem('loggingOut');

    // Redirect to home
    window.location.replace('/');
  }
};

const App = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const { pathname } = useLocation();

  useEffect(() => {
    // Add Google Analytics script
    const gaId = config.analytics.googleAnalyticsId;

    if (!gaId) {
      console.warn('Google Analytics ID is not defined in the configuration.');
      return;
    }

    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    document.head.appendChild(script);

    const inlineScript = document.createElement('script');
    inlineScript.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}');
    `;
    document.head.appendChild(inlineScript);
  }, []);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  useEffect(() => {
    setTimeout(() => setLoading(false), 1000);
  }, []);

  return loading ? (
    <Loader />
  ) : (
    <AuthProvider {...cognitoAuthConfig}>
      <SessionTimeoutHandler />
      <HistoryHandler />
      <Routes>
        <Route
          index
          path="/"
          element={
            <>
             <PageTitle title="Welcome" />
             
              <WelcomePage />
             
            </>
          }
        />
 
        <Route
          path="/auth/loading"
          element={
            <>
              <PageTitle title="Auth Loading" />
              <AuthLoading />
            </>
          }
        />

 <Route
          path="/login"
          element={
          
              <>
              <PageTitle title="Signin | Raaz Security" />
                <SignIn />
              </>
           
          }
        />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <>
                <PageTitle title="dashboard" />
                <Dashboard />
              </>
            </ProtectedRoute>
          }
        />

        <Route
          path="/domains/:domain"
          element={
            <ProtectedRoute>
              <>
                <PageTitle title="Assessment" />
                <AssessmentPage />
              </>
            </ProtectedRoute>
          }
        />

        <Route
          path="/payment"
          element={
              <ProtectedRoute>
            <>
              <PageTitle title="Payment" />
              <PaymentPage />
            </>
              </ProtectedRoute>
          }
        />

<Route
          path="/payment/success"
          element={
             <ProtectedRoute>
            <>
              <PageTitle title="Payment Success" />
              <PaymentSuccessPage />
            </>
             </ProtectedRoute>
          }
        />
<Route
  path="/payment/cancel"
  element={
    <>
      <PageTitle title="Payment Failed" />
      <PaymentFailurePage />
    </>
  }
/>

<Route
          path="/payment-history"
          element={
             <ProtectedRoute>
            <>
              <PageTitle title="payment History" />
              <PaymentHistory />
            </>
             </ProtectedRoute>
          }
        />

        
<Route
          path="/user-agreement"
          element={
              <ProtectedRoute>
            <>
              <PageTitle title="User Agreement" />
              <UserAgreement />
            </>
              </ProtectedRoute>
          }
        />

<Route
          path="/org"
          element={
              <ProtectedRoute>
            <>
              <PageTitle title="Company details" />
              <CompanyForm />
            </>
              </ProtectedRoute>
          }
        />

{/* l */}
        <Route
          path="/user-profile"
          element={
              <ProtectedRoute>
            <>
              <PageTitle title="UserProfile" />
              <UserProfile />
            </>
             </ProtectedRoute>
          }
        />
        <Route
          path="/instructions"
          element={
            <ProtectedRoute>
              <>
                <PageTitle title="Instructions" />
                <Instructions />
              </>
            </ProtectedRoute>
          }
        />

        <Route
          path="/questionapp"
          element={
            <ProtectedRoute>
              <>
                <PageTitle title="QuestionApp" />
                <QuestionApp />
              </>
            </ProtectedRoute>
          }
        />
        <Route
          path="/preview-users"
          element={
            <ProtectedRoute>
              <>
                <PageTitle title="Preview Users" />
                <PreviewUsersPage />
              </>
            </ProtectedRoute>
          }
        />
        <Route
          path="/auth/callback"
          element={
            <>
              <PageTitle title="Authentication" />
              <AuthLoading />
            </>
          }
        />
        {/* Catch-all route for any unmatched paths - but not for auth paths */}
        <Route
          path="*"
          element={
            <>
              <PageTitle title="Page Not Found" />
              <div className="flex flex-col items-center justify-center min-h-screen">
                <h1 className="text-2xl font-bold mb-4">Page Not Found</h1>
                <p className="text-gray-600 mb-4">The page you're looking for doesn't exist.</p>
                <button
                  onClick={() => window.location.href = '/assessment'}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Go Home
                </button>
              </div>
            </>
          }
        />
      </Routes>
    </AuthProvider>
  );
};

export default App;
