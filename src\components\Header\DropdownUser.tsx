import { useEffect, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import config from '../../config';
import { useAuth } from 'react-oidc-context';

const DropdownUser: React.FC = () => {
  /**
   * State to manage the open/close status of the dropdown menu.
   * @type {boolean}
   */
  const [dropdownOpen, setDropdownOpen] = useState(false);

  /**
   * Reference to the trigger element (anchor element).
   * @type {React.RefObject<HTMLAnchorElement>}
   */
  const trigger = useRef<HTMLDivElement>(null); // if using <div>

  /**
   * Reference to the dropdown menu element (div element).
   * @type {React.RefObject<HTMLDivElement>}
   */
  const dropdown = useRef<HTMLDivElement>(null);

  /**
   * Retrieve the username from auth.
   * @type {string | null}
   */ const auth = useAuth();
  const username =
    `${auth.user?.profile?.given_name || ''} ${
      auth.user?.profile?.family_name || ''
    }`.trim() || 'User';

  /**
   * Hook to navigate programmatically.
   * @type {NavigateFunction}
   */
  const navigate = useNavigate();

  // Define an asynchronous function to handle user logout
    // Define an asynchronous function to handle user logout
  const handleLogout = async () => {
    const { clientId, cognitoDomain, signoutUri } = config.cognito;

    // Set logout flag first
    sessionStorage.setItem('loggingOut', 'true');
    
    // Clear cookies
    document.cookie.split(';').forEach((cookie) => {
      const [cookieName] = cookie.trim().split('=');
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`;
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}`;
    });
    
    // Clear any local/session storage related to auth
    Object.keys(localStorage).forEach(key => {
      if (key.includes('auth') || key.includes('oidc') || key.includes('user') || key.includes('pkce')) {
        localStorage.removeItem(key);
      }
    });
    
    Object.keys(sessionStorage).forEach(key => {
      if (key.includes('auth') || key.includes('oidc') || key.includes('user')) {
        sessionStorage.removeItem(key);
      }
    });
    
    const logoutUrl = `${cognitoDomain}/logout?client_id=${clientId}&logout_uri=${encodeURIComponent(
      decodeURIComponent(signoutUri)
    )}&response_type=code`;

    if (auth && auth.removeUser) {
      try {
        await auth.removeUser();
        await auth.signoutRedirect({
          extraQueryParams: {
            client_id: clientId,
            logout_uri: signoutUri,
            response_type: "code",
          },
        });
      } catch (e) {
        console.error("Error during signoutRedirect:", e);
        window.location.href = logoutUrl;
      }
    } else {
      window.location.href = logoutUrl;
    }
  };

  // close on click outside
  // useEffect hook to handle clicks outside the dropdown to close it
  useEffect(() => {
    // Define a click handler function
    const clickHandler = ({ target }: MouseEvent) => {
      // If the dropdown reference is not set, do nothing
      if (!dropdown.current) return;

      // If the dropdown is not open, or the click is inside the dropdown or trigger element, do nothing
      if (
        !dropdownOpen ||
        dropdown.current.contains(target as Node) ||
        (trigger.current && trigger.current.contains(target as Node))
      )
        return;

      // Otherwise, close the dropdown
      setDropdownOpen(false);
    };

    // Add the click handler to the document
    document.addEventListener('click', clickHandler);

    // Cleanup function to remove the click handler when the component unmounts or dropdownOpen changes
    return () => document.removeEventListener('click', clickHandler);
  }, [dropdownOpen]); // Dependency array to re-run the effect when dropdownOpen changes

  // useEffect hook to handle the Escape key press to close the dropdown
  useEffect(() => {
    // Define a key handler function
    const keyHandler = ({ keyCode }: KeyboardEvent) => {
      // If the dropdown is not open or the pressed key is not Escape (key code 27), do nothing
      if (!dropdownOpen || keyCode !== 27) return;

      // Otherwise, close the dropdown
      setDropdownOpen(false);
    };

    // Add the key handler to the document
    document.addEventListener('keydown', keyHandler);

    // Cleanup function to remove the key handler when the component unmounts or dropdownOpen changes
    return () => document.removeEventListener('keydown', keyHandler);
  }, [dropdownOpen]); // Dependency array to re-run the effect when dropdownOpen changes

  // navigate to profile page
  const handleUserProfile = () => {
    navigate('/user-profile'); // Navigate to the User Profile page
  };

  const handlePaymentHistory = () => {
    navigate('/payment-history'); // Navigate to the User Profile page
  };

  return (
    <div className="relative">
      {/* // Link component that serves as the trigger for the dropdown menu */}
      <div
        ref={trigger}
        onClick={() => setDropdownOpen(!dropdownOpen)}
        className="flex items-center cursor-pointer"
      >
        {/* Container for the text content, with responsive text alignment */}
        <span className="text-right lg:text-left">
          {/* Welcome message with styling */}
          <span className="block text-sm font-medium text-black dark:text-white mr-2">
            Welcome, <span className="font-bold">{username}</span>
          </span>
        </span>

        <svg
          className="fill-current sm:block"
          width="12"
          height="8"
          viewBox="0 0 12 8"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M0.410765 0.910734C0.736202 0.585297 1.26384 0.585297 1.58928 0.910734L6.00002 5.32148L10.4108 0.910734C10.7362 0.585297 11.2638 0.585297 11.5893 0.910734C11.9147 1.23617 11.9147 1.76381 11.5893 2.08924L6.58928 7.08924C6.26384 7.41468 5.7362 7.41468 5.41077 7.08924L0.410765 2.08924C0.0853277 1.76381 0.0853277 1.23617 0.410765 0.910734Z"
            fill=""
          />
        </svg>
      </div>
      {/* <!-- Dropdown Start --> */}
      <div
        ref={dropdown}
        onFocus={() => setDropdownOpen(true)}
        onBlur={() => setDropdownOpen(false)}
        className={`absolute right-0 mt-4 flex w-62.5 flex-col rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark ${
          dropdownOpen === true ? 'block' : 'hidden'
        }`}
      >
        <button
          onClick={handleUserProfile} // Add handler for User Profile
          className="flex items-center gap-3.5 px-6 py-4 text-sm font-medium duration-300 ease-in-out hover:text-primary lg:text-base"
        >
          User Profile
        </button>

        <button
          onClick={handlePaymentHistory} // Add handler for User Profile
          className="flex items-center gap-3.5 px-6 py-4 text-sm font-medium duration-300 ease-in-out hover:text-primary lg:text-base"
        >
          Payments
        </button>
        <button
          onClick={handleLogout}
          className="flex items-center gap-3.5 px-6 py-4 text-sm font-medium duration-300 ease-in-out hover:text-primary lg:text-base"
        >
          Log Out
        </button>
      </div>
      {/* <!-- Dropdown End --> */}
    </div>
  );
};

export default DropdownUser;
