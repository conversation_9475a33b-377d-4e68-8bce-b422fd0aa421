import React, { useEffect, useState } from "react";
import Slider from "react-slick"; // React Slick Slider
import { <PERSON><PERSON><PERSON> as M<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { AuthContextProps } from "react-oidc-context";
import { Box, Typography, useMediaQuery } from "@mui/material";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import config from "../config";

interface Dataset {
  label: string;
  data: number[];
}

interface ChartData {
  labels: string[];
  datasets: Dataset[];
}

const BarChartWithCarousel = ({ auth }: { auth: AuthContextProps }) => {
  const [chartData, setChartData] = useState<ChartData>({
    labels: [],
    datasets: [],
  });

  const isMobile = useMediaQuery("(max-width: 600px)"); // More accurate mobile breakpoint
  const isTablet = useMediaQuery("(min-width: 601px) and (max-width: 1024px)"); // Ensures tablets are between mobile and desktop
  const isDesktop = useMediaQuery("(min-width: 1025px)"); // Handles larger screens
  

  const settings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: isMobile ? 1 : isTablet ? 2 : isDesktop ? 3 : 4, // Desktop can show 3-4 depending on preference
    slidesToScroll: 1,
  };

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user?.access_token) {
      return auth.user.access_token;
    }
    console.warn("User is not authenticated or token is missing.");
    return null;
  };

  useEffect(() => {
    const fetchChartData = async () => {
      const token = await getAccessToken();
      if (!token) {
        console.error("No access token. Skipping API request.");
        return;
      }

      try {
        const response = await fetch(`${config.userService.barChart}`, {
          headers: {
            Authorization: `Bearer ${token}`,
            accept: "*/*",
          },
        });

        if (!response.ok) {
          throw new Error(`Error fetching chart data: ${response.statusText}`);
        }

        const data: ChartData = await response.json();
        setChartData(data);
      } catch (error) {
        console.error("Error fetching chart data:", error);
      }
    };

    fetchChartData();
  }, [auth]);

  const { labels, datasets } = chartData;

  const datasetColors: { [key: string]: string } = {
    Met: "#74DFF7",
    "Not Met": "#F97D86",
    Partial: "#F7C67F",
  };

  return (

    // deployment test
    
    <Box
  sx={{
    padding: { xs: "1rem", sm: "1.5rem", md: "2rem" }, // Adjusts padding based on screen size
    overflowX: "hidden",
    height: "auto",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    width: "100%",
  }}
>

      <Typography variant="h6" sx={{ textAlign: "center", whiteSpace: "normal", wordBreak: "break-word" }}>
        Assessment Overview
      </Typography>

      <Box sx={{ width: "100%", maxWidth: "100%" }}>

        <Slider {...settings}>
          {labels.map((label, index) => (
           <Box
           key={index}
           sx={{
             padding: { xs: "0.5rem", sm: "0.75rem", md: "1rem" }, // Adjusts padding based on screen size
             display: "flex",
             flexDirection: "column",
             alignItems: "center",
             textAlign: "center",
             overflow: "visible",
           }}
         >
         
              <MUIBarChart
                series={datasets.map((dataset) => ({
                  data: [dataset.data[index]],
                  label: dataset.label,
                  color: datasetColors[dataset.label] || "#000",
                }))}
                xAxis={[
                  {
                    scaleType: "band",
                    data: [label],
                    labelStyle: {
                      fontSize: isMobile ? 8 : isTablet ? 10 : isDesktop ? 14 : 16, 
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }
                    
                  },
                ]}
                height={300}
                width={isMobile ? 280 : isTablet ? 350 : isDesktop ? 450 : 500}

              />
              <Typography variant="body2" sx={{ marginTop: "10px", whiteSpace: "normal", wordBreak: "break-word" }}>
                {label}
              </Typography>
            </Box>
          ))}
        </Slider>
      </Box>
    </Box>
  );
};

export default BarChartWithCarousel;
