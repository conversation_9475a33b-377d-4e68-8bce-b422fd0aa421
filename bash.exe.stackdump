Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FEBA
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210285FF9, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBB0  0002100690B4 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBE90  00021006A49D (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC0D0D0000 ntdll.dll
7FFC0C4C0000 KERNEL32.DLL
7FFC0AB40000 KERNELBASE.dll
7FFC0B3C0000 USER32.dll
7FFC0AA70000 win32u.dll
7FFC0B1E0000 GDI32.dll
7FFC0AFD0000 gdi32full.dll
7FFC0AAA0000 msvcp_win.dll
7FFC0A970000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC0B310000 advapi32.dll
7FFC0B270000 msvcrt.dll
7FFC0B6E0000 sechost.dll
7FFC0CE50000 RPCRT4.dll
7FFC0A770000 bcrypt.dll
7FFC0A030000 CRYPTBASE.DLL
7FFC0AF40000 bcryptPrimitives.dll
7FFC0B100000 IMM32.DLL
