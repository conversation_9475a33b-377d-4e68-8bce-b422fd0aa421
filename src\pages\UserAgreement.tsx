import React, { useEffect, useState } from 'react';
import DefaultLayout from '../layout/DefaultLayout';
import { useAuth } from 'react-oidc-context';
import config from '../config';
import { useNavigate } from 'react-router-dom';

const UserAgreement: React.FC = () => {
  const [agreed, setAgreed] = useState(false);
  const auth = useAuth();
  const navigate = useNavigate();

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    console.error('User is not authenticated. Cannot fetch token.');
    return null;
  };

  const checkAccess = async () => {
    if (!auth.isAuthenticated || !auth.user) return;

    try {
      const token = await getAccessToken();
      const response = await fetch(`${config.userService.accessCheck}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          accept: '*/*',
        },
      });

      if (!response.ok) throw new Error(`Error: ${response.status}`);

      const data = await response.json(); // Parse API response

      // Navigate to /org if either access is denied or org_details_saved is false
      if (!data.hasAgreedToAgreement) {
        navigate('/user-agreement', { replace: true });
      } else if (!data.org_details_saved) {
        navigate('/org', { replace: true });
      } else if (!data.accessGranted) {
        navigate('/payment', { replace: true });
      } else {
        navigate('/dashboard', { replace: true });
      }
    } catch (error) {
      console.error('Access check failed:', error);
    }
  };

  const handleAgreement = async () => {
    if (!auth.isAuthenticated || !auth.user) {
      console.error('User is not authenticated.');
      navigate('/'); // Redirect to login if not authenticated
      return;
    }

    try {
      const token = await getAccessToken();
      const response = await fetch(`${config.userService.UserAgreement}`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: '*/*',
        },
      });

      if (!response.ok) {
        throw new Error(
          `Failed to update agreement status. Status: ${response.status}`,
        );
      }

      console.log('✅ Agreement API Call Successful');

      // After successfully posting the agreement, call checkAccess()
      checkAccess();
    } catch (error) {
      console.error('Error during agreement API call:', error);
    }
  };

  return (
    <DefaultLayout>
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow-md rounded-lg max-w-3xl w-full p-6 sm:p-8 overflow-hidden">
          {/* Title */}
          <div>
            <div className="max-w-4xl mx-auto p-6 bg-white shadow rounded-lg">
              <div className="text-3xl font-bold text-center text-blue-700 mb-6">
                END USER LICENSE AGREEMENT
              </div>

              <div className="mb-4 text-sm text-gray-700">
                <p className="mb-4">
                  <strong>IMPORTANT</strong>
                </p>
                <p className="mb-4">
                  Raaz Security Services, Inc., New York ("Licensor") licenses
                  this software and all associated documentation (the
                  "Software") for nonexclusive use by the end user (herein
                  called "Licensee"). Licensee has read this End User Software
                  License Agreement (the "License") and understands, accepts and
                  expressly agrees to abide by the terms and conditions of this
                  License. By using the Software, Licensee accepts and agrees
                  that Licensee will abide by, and is legally bound by, the
                  terms of this License. If Licensee does not agree to abide by
                  the terms of this License, Licensee shall not install, copy or
                  use the Software.
                </p>
                <p className="mb-4">
                  Please read this End User License Agreement ("Agreement")
                  carefully before using the software ("Software") provided by
                  Raaz Security Services, Inc. ("Licensor").
                </p>
                <p className="mb-4">
                  By installing or using the Software, you agree to be bound by
                  the terms and conditions of this Agreement.
                </p>

                <div className="font-semibold text-lg text-gray-800 mt-6 mb-2">
                  1. License Grant and Restrictions
                </div>
                <p className="mb-2">
                  The Licensor grants you a non-exclusive, non-transferable
                  license to use the Software on a single device or as otherwise
                  specified in the accompanying documentation. Under the terms
                  of this non-exclusive, non-transferable (except as
                  specifically permitted herein) License:
                </p>
                <ul className="list-decimal pl-6 mb-4">
                  <li>
                    Licensee may use a machine-readable form of the Software on
                    a single computer at a time and only for the operation of
                    Licensor products.
                  </li>
                  <li>
                    Licensee shall not modify, translate, create derivative
                    works, decompile, disassemble, or reverse engineer the
                    Software.
                  </li>
                  <li>
                    Licensee shall not sublicense, lease, sell, assign, pledge
                    or otherwise transfer, make available or share the Software
                    with any third party or entity without Licensor's prior
                    written consent.
                  </li>
                  <li>
                    Modify, reverse-engineer, decompile, or disassemble the
                    Software, except as permitted by applicable law.
                  </li>
                  <li>
                    Rent, lease, sublicense, or transfer the Software to any
                    third party.
                  </li>
                  <li>
                    Use the Software in any manner that could damage, disable,
                    overburden, or interfere with another party’s use.
                  </li>
                  <li>
                    Copy, distribute, or publish any part of the Software,
                    except for backup copies as permitted under this Agreement.
                  </li>
                </ul>

                <div className="font-semibold text-lg text-gray-800 mt-6 mb-2">
                  2. Transfer or Ownership
                </div>
                <p className="mb-4">
                  There is no transfer of ownership of the license. The Software
                  is licensed, not sold. All rights, title, and interest in the
                  Software, including all intellectual property rights, belong
                  to the Licensor.
                </p>
                <p className="mb-4">
                  The Software is owned and copyrighted by Licensor. Except for
                  the rights expressly granted herein, Licensor retain all
                  rights in or to the Software, including, without limitation,
                  all right title and interest in or to all copyright, patent,
                  trade secret, and other intellectual and proprietary rights
                  therein, and any copies thereof, in whole or in part, all of
                  which are the valuable property of Licensor and/or its
                  suppliers. Licensee may not remove, change, or delete the
                  copyright notice from the Software. If Licensee makes any
                  copies of the Software in whole or in part, all such copies
                  shall contain the same copyright and proprietary markings as
                  appear on or in the original Software copy. Licensee will
                  instruct its employees and others having access to the
                  Software in, and ensure their compliance with the terms of,
                  this License. Licensee shall use its best efforts to prevent
                  any unauthorized copying of the Software. Licensee shall be
                  responsible for any breach of any provision of this License by
                  Licensee's employees. Licensee shall not sell, transfer,
                  publish, disclose, commercially exploit or otherwise make
                  available, the whole or any part of the Software, or any
                  copies thereof, to any third party or persons not permitted by
                  the terms of, and pursuant to the terms contained in this
                  License. Licensee is not in violation of this Agreement,
                  including this section, when a third party views the
                  functional output resulting from Licensee's use of the
                  Software.
                </p>

                <div className="font-semibold text-lg text-gray-800 mt-6 mb-2">
                  3. Updates and Upgrades
                </div>
                <p className="mb-4">
                  The Licensor may provide updates or upgrades at its
                  discretion, which may be subject to additional terms.
                </p>
                <p className="mb-4 font-bold uppercase">
                  THE CLOUD BASED SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY
                  OF ANY KIND INCLUDING WARRANTIES THAT THE SOFTWARE IS ERROR
                  FREE OR WILL RUN UNINTERRUPTED, OR WARRANTIES OF
                  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR AGAINST
                  INFRINGEMENT, WHICH ARE HEREBY DISCLAIMED.
                </p>
                <p className="mb-4">
                  Licensor shall be liable under the terms of this License only
                  in accordance with the following provisions:
                </p>
                <ul className="list-decimal pl-6 mb-4">
                  <li>
                    Licensor shall not be liable for any infringement of
                    third-party rights by the Software.
                  </li>
                  <li>
                    Licensor does not warrant the functions provided by the
                    Software and Licensee shall be responsible to verify any
                    results obtained from the use of the Software, in particular
                    with respect to accuracy, safety and security. Licensee is
                    solely responsible for the selection of the Software to
                    achieve Licensee's intended results, and for the
                    installation, use, and results obtained from the Software.
                  </li>
                  <li>
                    THE SOFTWARE IS NOT DESIGNED, INTENDED, OR AUTHORIZED FOR
                    USE IN ANY TYPE OF SYSTEM OR APPLICATION IN WHICH THE
                    FAILURE OF THE SOFTWARE COULD CREATE A SITUATION WHERE
                    PERSONAL INJURY OR DEATH MAY OCCUR (e.g., MEDICAL SYSTEMS,
                    LIFE SUSTAINING OR LIFE SAVING SYSTEMS). Should Licensee use
                    the Software for any such unintended or unauthorized use,
                    Licensee hereby indemnifies, defends, and holds Licensor and
                    its officers, subsidiaries and affiliates harmless against
                    all claims, costs, damages, and expenses, and reasonable
                    attorney fees arising out of, directly or indirectly, such
                    use and any claim of product liability, personal injury or
                    death associated with such unintended or unauthorized use,
                    even if such claim alleges that Licensor was negligent
                    regarding the design or manufacture of the Software.
                  </li>
                </ul>

                <div className="font-semibold text-lg text-gray-800 mt-6 mb-2">
                  4. Termination
                </div>
                <p className="mb-4">
                  This Agreement remains effective until terminated. You may
                  terminate this Agreement by requesting termination of the
                  account via an email.
                </p>
                <p className="mb-4">
                  Licensor may terminate this License if Licensee fails to
                  comply with its terms and conditions in any material respect.
                  Upon any termination, Licensee may not use the Software and
                  must return or destroy all whole and partial copies thereof.
                  Upon any termination of this License, Licensee shall
                  immediately destroy the Software or return it to Licensor,
                  along with any copies made by Licensee, and delete any
                  installed copies from Licensee’s hardware.
                </p>

                <div className="font-semibold text-lg text-gray-800 mt-6 mb-2">
                  5. No Warranty
                </div>
                <p className="mb-4 font-bold uppercase">
                  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND
                  INCLUDING WARRANTIES THAT THE SOFTWARE IS ERROR FREE OR WILL
                  RUN UNINTERRUPTED, OR WARRANTIES OF MERCHANTABILITY, FITNESS
                  FOR A PARTICULAR PURPOSE OR AGAINST INFRINGEMENT, WHICH ARE
                  HEREBY DISCLAIMED. THE LICENSOR MAKES NO REPRESENTATIONS OR
                  WARRANTIES REGARDING ITS FUNCTIONALITY OR RELIABILITY.
                </p>
                <p className="mb-4">
                  Licensor shall not be liable for any infringement of
                  third-party rights by the Software.
                </p>
                <p className="mb-4">
                  Licensor does not warrant the functions provided by the
                  Software and Licensee shall be responsible to verify any
                  results obtained from the use of the Software, in particular
                  with respect to accuracy, safety and security. Licensee is
                  solely responsible for the selection of the Software to
                  achieve Licensee's intended results, use, and results obtained
                  from the Software.
                </p>
                <p className="mb-4 font-bold">
                  THE SOFTWARE IS NOT DESIGNED, INTENDED, OR AUTHORIZED FOR USE
                  IN ANY TYPE OF SYSTEM OR APPLICATION IN WHICH THE FAILURE OF
                  THE SOFTWARE COULD CREATE A SITUATION WHERE PERSONAL INJURY OR
                  DEATH MAY OCCUR (e.g., MEDICAL SYSTEMS, LIFE SUSTAINING OR
                  LIFE SAVING SYSTEMS). Should Licensee use the Software for any
                  such unintended or unauthorized use, Licensee hereby
                  indemnifies, defends, and holds Licensor and its officers,
                  subsidiaries and affiliates harmless against all claims,
                  costs, damages, and expenses, and reasonable attorney fees
                  arising out of, directly or indirectly, such use and any claim
                  of product liability, personal injury or death associated with
                  such unintended or unauthorized use, even if such claim
                  alleges that Licensor was negligent regarding the design or
                  manufacture of the Software.
                </p>

                <div className="font-semibold text-lg text-gray-800 mt-6 mb-2">
                  6. Limitation of Liability
                </div>
                <p className="mb-4">
                  The Licensor shall not be liable for any indirect, incidental,
                  special, or consequential damages arising from the use of the
                  Software.
                </p>

                <div className="font-semibold text-lg text-gray-800 mt-6 mb-2">
                  7. Governing Law
                </div>
                <p className="mb-4">
                  Licensee will not use the Software in any manner prohibited by
                  applicable law including any restrictions imposed by the
                  United States Government. The Software is provided with
                  restricted rights.
                </p>

                <div className="font-semibold text-lg text-gray-800 mt-6 mb-2">
                  8. Miscellaneous
                </div>
                <ul className="list-decimal pl-6 mb-4">
                  <li>
                    <strong>Entire Agreement:</strong> This Agreement
                    constitutes the entire understanding between the parties.
                  </li>
                  <li>
                    <strong>Severability:</strong> If any provision is invalid,
                    the remaining provisions will remain in effect.
                  </li>
                  <li>
                    <strong>Amendments:</strong> The Licensor may modify this
                    Agreement, effective upon posting.
                  </li>
                </ul>
                <p className="mb-4">
                  By using the Software, you acknowledge that you have read,
                  understood, and agree to the terms of this Agreement.
                </p>

                <div className="font-semibold text-lg text-gray-800 mt-6 mb-2">
                  9. Entire Agreement / Severability
                </div>
                <p className="mb-4">
                  This License, any Licensors Standard Terms and Conditions as
                  applicable from time to time, as well as all exhibits,
                  schedules or appendices hereto, constitutes the complete and
                  exclusive statement of the terms hereof and supersedes all
                  prior oral and written statements of any kind made by the
                  parties or their representatives with respect to the subject
                  matter hereof. Any Customer / Licensee purchase order or
                  similar document issued by Licensee shall not be part of this
                  License and shall not add to or modify any of the terms
                  hereof. This License may only be changed or supplemented by a
                  written amendment signed by authorized representatives of the
                  parties. If any provision of this License is held to be void,
                  invalid, unenforceable or illegal, the other provisions shall
                  continue in full force and effect.
                </p>
              </div>
            </div>
          </div>

          {/* Agree Checkbox */}
          <div className="flex items-center mt-4">
            <input
              type="checkbox"
              id="agree"
              className="w-5 h-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              checked={agreed}
              onChange={() => setAgreed(!agreed)}
            />
            <label
              htmlFor="agree"
              className="ml-2 text-gray-700 text-sm sm:text-base"
            >
              I have read and agree to the terms and conditions.
            </label>
          </div>

          {/* Submit Button */}
          <button
            className={`mt-4 w-full px-6 py-3 text-white font-semibold rounded-lg transition duration-300 ${
              agreed
                ? 'bg-blue-600 hover:bg-blue-700'
                : 'bg-gray-400 cursor-not-allowed'
            }`}
            disabled={!agreed}
            onClick={handleAgreement} // ✅ Call handleAgreement instead
          >
            Agree & Continue
          </button>
        </div>
      </div>
    </DefaultLayout>
  );
};

export default UserAgreement;
