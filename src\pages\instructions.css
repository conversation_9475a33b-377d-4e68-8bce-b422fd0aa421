.top-container {
    width: 100%; /* Full width by default for responsiveness */
    max-width: 100%; /* Maximum width for large screens */
    max-height: 100%; /* Default height */
    background: #002574; /* Background color */
    margin: 0 auto; /* Center the container */
    padding: 20px; /* Internal padding */
  }


  .cmmc {
    
    font-size: 96px; /* Default font size for larger screens (1920x1080) */
    font-weight: 700;
    line-height: 144px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
  .btn-assessment {
    background: #00C1A0;
    width: 90%; /* Default width */
    max-width: 100%; /* Ensure it doesn't exceed a certain size */
    height: auto; /* Let height adjust based on content */
    padding: 16px; /* Default padding */
    border-radius: 8px; /* Rounded corners */
    opacity: 1; /* Set visible by default */
    margin: 0 auto; /* Center align on smaller screens */
    display: flex;
    align-items: center;
  justify-content: center;
  }
  
  @media (min-width: 640px) { /* Small screens and above */
    .btn-assessment {
      padding: 24px;
      width: 80%; /* Slightly wider on larger screens */
    }
  }
  
  @media (min-width: 1024px) { /* Large screens and above */
    .btn-assessment {
      width: 100%; /* Fixed width for large screens */
      height: 100%; /* Fixed height */
      padding: 32px;
    }
  }
  

  .top-section-h1-c {
    background: #00c1a0;
    width: 90%; /* Scaled for responsiveness */
    max-width: 515px;
    height: auto;
    padding: 10px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .top-section-h1-span-level-1,
  .top-section-h1-span-assessment {
    
    font-size: 4vw; /* Scaled for responsiveness */
    font-weight: 700;
    line-height: 1.5;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
  
  .top-section-h1-span-assessment {
    font-weight: 400;
  }
  
  .welcome {
    
    font-size: 32px; /* Default font size for larger screens (1920x1080) */
    font-weight: 400;
    line-height: 48px; /* Default line height */
    text-align: left; /* Default alignment */
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #1CF1CC; /* Default text color */
  }
  .w-p {
    
    font-size: 22px; /* Default font size for larger screens (1920x1080) */
    font-weight: 400;
    line-height: 33px; /* Default line height */
    text-align: left; /* Default alignment */
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .w-p {
      font-size: 20px; /* Slightly smaller font size */
      line-height: 30px; /* Adjusted line height */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .w-p {
      font-size: 18px; /* Further reduced font size */
      line-height: 27px; /* Adjusted line height */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .w-p {
      font-size: 16px; /* Smallest font size for phones */
      line-height: 24px; /* Adjusted line height */
      text-align: center; /* Center align for better readability on small screens */
    }
  }
  
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .welcome {
      font-size: 28px; /* Slightly smaller font size */
      line-height: 42px; /* Adjusted line height */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .welcome {
      font-size: 24px; /* Further reduced font size */
      line-height: 36px; /* Adjusted line height */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .welcome {
      font-size: 20px; /* Smallest font size for phones */
      line-height: 30px; /* Adjusted line height */
      text-align: center; /* Center text for better readability on small screens */
    }
  }
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .cmmc {
      font-size: 72px; /* Slightly reduced font size */
      line-height: 108px; /* Adjusted line height */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .cmmc {
      font-size: 48px; /* Further reduced font size */
      line-height: 72px; /* Adjusted line height */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .cmmc {
      font-size: 36px; /* Smallest font size for phones */
      line-height: 54px; /* Adjusted line height */
      text-align: center; /* Center text on small screens for better readability */
    }
  }
  
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .top-container {
      max-width: 100%; /* Adjusted maximum width */
      height: 100%; /* Reduced height */
      padding: 16px; /* Slightly smaller padding */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .top-container {
      max-width: 100%; /* Smaller width */
      height: 100%; /* Reduced height */
      padding: 12px; /* Smaller padding */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .top-container {
      width: 90%; /* Take up 90% of the screen width */
      height: 100%; /* Reduced height */
      padding: 10px; /* Minimal padding */
    }
  }

 /* Default for 1920x1080 screens */
.img-instruction {
  width: 364px;
  height: 113px;
}

/* For screens smaller than 1440px */
@media (max-width: 1440px) {
  .img-instruction {
      width: 300px;
      height: 94px;
  }
}

/* For screens smaller than 1024px */
@media (max-width: 1024px) {
  .img-instruction {
      width: 250px;
      height: 78px;
  }
}

/* For screens smaller than 768px (tablets) */
@media (max-width: 768px) {
  .img-instruction {
      width: 200px;
      height: 62px;
  }
}

/* For screens smaller than 480px (mobile phones) */
@media (max-width: 480px) {
  .img-instruction {
      width: 150px;
      height: 47px;
  }
}

/* Default for 1920x1080 screens */
.start-assessment {
  width: 367px;
  height: 91px;
  flex-shrink: 0;
  border-radius: 8px;
  background: linear-gradient(90deg, #002574 0%, #0046DA 100%);
  color: #FFF;
  
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

/* Responsive styles for smaller screens */
@media (max-width: 1440px) {
  .start-assessment {
    width: 300px;
    height: 80px;
    font-size: 18px;
  }
}

@media (max-width: 1024px) {
  .start-assessment {
    width: 250px;
    height: 70px;
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .start-assessment {
    width: 200px;
    height: 60px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .start-assessment {
    width: 150px;
    height: 50px;
    font-size: 12px;
  }
}

/* Heading styling */
.i-h {
  color: #303030;
  
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-align: center;
}

/* Paragraph styling */
.i-p {
  color: #000;
  
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-align: start;
}

/* Responsive styles for headings and paragraphs */
@media (max-width: 1024px) {
  .i-h {
    font-size: 16px;
  }
  .i-p {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .i-h {
    font-size: 14px;
  }
  .i-p {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .i-h {
    font-size: 12px;
  }
  .i-p {
    font-size: 10px;
  }
}

  