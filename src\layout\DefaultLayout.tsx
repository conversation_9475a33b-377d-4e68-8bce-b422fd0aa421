import React, { useState, ReactNode } from 'react';
import Header from '../components/Header/index';
import EmailIcon from '@mui/icons-material/Email';
import <PERSON>Logo from '../images/logo/AB_logo.png';

const DefaultLayout: React.FC<{ children: ReactNode; refreshKey?: number }> = ({
  children,
  refreshKey,
}) => {
  return (
    <div className="dark:bg-boxdark-2 dark:text-bodydark flex h-screen w-screen flex-col">
      {/* Content Area */}
      <div className="flex flex-1 flex-col overflow-y-auto">
        {/* 🔄 Pass refreshKey to Header */}
        <Header refreshKey={refreshKey || 0} />
        <main className="flex-1 w-full px-4 py-6">{children}</main>
      </div>

      {/* Footer */}

     <footer className="bg-white w-full py-4 px-4 text-sm overflow-x-auto whitespace-nowrap">
  <div className="flex flex-row justify-between items-center gap-6 min-w-max">

    {/* Logo + Copyright */}
    <div className="flex items-center space-x-2">
      <img src={ABLogo} alt="AB Logo" className="h-14 w-auto" />
      <p className="text-[#002574] font-roboto font-normal">
        © {new Date().getFullYear()} Raaz Security Services, Inc.
      </p>
    </div>

    {/* Trademark */}
    <p className="text-[#002574] font-roboto font-normal">
      CMMCgenie is a registered trademark of Raaz Security Services, Inc.
    </p>

    {/* Contact Info */}
    <p className="text-[#2B2B2B] font-roboto font-normal flex items-center">
      Need help? Contact us at
      <EmailIcon className="ml-2 mr-1" style={{ color: '#00C1A0' }} />
      <a
        href="mailto:<EMAIL>"
        className="text-[#002574] underline"
      >
        <EMAIL>
      </a>
    </p>

  </div>
</footer>

    </div>
  );
};

export default DefaultLayout;
