import React, { useState } from "react";
import { CardElement, useElements, useStripe } from "@stripe/react-stripe-js";

const CheckoutForm: React.FC = () => {
  const stripe = useStripe();
  const elements = useElements();
  const [paymentStatus, setPaymentStatus] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!stripe || !elements) return;

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) return;

    // Simulating a payment method creation (test only)
    const { error, paymentMethod } = await stripe.createPaymentMethod({
      type: "card",
      card: cardElement,
    });

    if (error) {
      console.error("Payment failed:", error);
      setPaymentStatus("Payment Failed. Please try again.");
    } else {
      console.log("Payment method created successfully:", paymentMethod);
      setPaymentStatus("Payment successful! (Test Mode)");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <CardElement className="p-2 border rounded-md" />
      <button
        type="submit"
        disabled={!stripe}
        className="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600"
      >
        Pay Now
      </button>
      {paymentStatus && (
        <div className="mt-4 p-2 border rounded bg-gray-100 text-center">
          {paymentStatus}
        </div>
      )}
    </form>
  );
};

export default CheckoutForm;
