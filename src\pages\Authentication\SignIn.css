/* Base Styles */
body {
   
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
  }
  
  /* Left section - blue background */
  .left-section {
    background-color: #002574;
    color: #fff;
    width: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .left-section-h1 {
    
    font-size: 6vw; /* Scaled for responsiveness */
    font-weight: 700;
    line-height: 1.5;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
  
  .left-section-h1-c {
    background: #00c1a0;
    width: 90%; /* Scaled for responsiveness */
    max-width: 515px;
    height: auto;
    padding: 10px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .left-section-h1-span-level-1,
  .left-section-h1-span-assessment {
    
    font-size: 4vw; /* Scaled for responsiveness */
    font-weight: 700;
    line-height: 1.5;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
  
  .left-section-h1-span-assessment {
    font-weight: 400;
  }
  
  .left-section-welcome {
    
    font-size: 4vw; /* Scaled for responsiveness */
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
  }
  
  .left-section-p {
    
    font-size: 3vw; /* Scaled for responsiveness */
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    margin-top: 10px;
  }
  
  .left-section .dots {
    gap: 5px;
    margin-top: 10px;
  }
  
  .left-section .dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
  
  .left-section .dots span.active {
    background-color: #fff;
  }
  
  /* Right section - login and registration */
  .right-section {
    width: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  
  .logo {
    width: 50%; /* Reduced for smaller screens */
    max-width: 150px; /* Adjust max-width for responsiveness */
    height: auto;
  }
  
  .right-section-automation {
    
    font-size: 20px; /* Default size for larger screens (1920x1080) */
    font-weight: 400;
    line-height: 30px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
  
  /* Divider */
  .divider {
    width: 3px; /* Default width for larger screens (1920x1080) */
    height: 50px; /* Default height */
    background: #00c1a0; /* Divider color */
    gap: 0px;
    opacity: 0px;
    position: relative;
  }
  
  /* Register Forgot */
  .register-forgot {
    
    font-size: 3vw; /* Scaled for responsiveness */
    line-height: 1.5;
    text-align: left;
    text-decoration-line: underline;
    text-decoration-style: solid;
  }
  
  /* Buttons and Links */
  .right-section h2 {
    font-size: 4vw; /* Scaled for responsiveness */
    margin-bottom: 20px;
    color: #002855;
  }
  
  .right-section .login-button {
    width: 100%;
    padding: 10px;
    background-color: #00c98d;
    color: #fff;
    font-size: 3vw; /* Scaled for responsiveness */
    border: none;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 15px;
  }
  
  .right-section .register {
    margin-top: 10px;
    font-size: 3vw; /* Scaled for responsiveness */
  }
  
  .right-section .register a {
    color: #00c98d;
    text-decoration: none;
    font-weight: bold;
  }

  .login {
    background: #00c1a0; /* Default background color */
    width: 100%; /* Full width by default for responsiveness */
    max-width: 715px; /* Maximum width for larger screens */
    height: 50px; /* Default height */
    padding: 10px 20px; /* Internal padding for button text */
    font-size: 18px; /* Default font size */
    font-weight: 600;
    text-align: center;
    border: none;
    border-radius: 8px;
    transition: background-color 0.3s ease; /* Smooth transition for hover effect */
  }
  
  /* Hover effect */
  .login:hover {
    background-color: #00a676; /* Darker green on hover */
  }
  
  /* For medium screens (tablets or smaller laptops, 1280px - 1919px) */
  @media (max-width: 1919px) and (min-width: 1280px) {
    .login {
      max-width: 600px; /* Slightly smaller width */
      height: 45px; /* Adjusted height */
      font-size: 16px; /* Reduced font size */
    }
  }
  
  /* For small screens (large phones, 768px - 1279px) */
  @media (max-width: 1279px) and (min-width: 768px) {
    .login {
      max-width: 500px; /* Smaller width */
      height: 40px; /* Reduced height */
      font-size: 14px; /* Smaller font size */
    }
  }
  
  /* For very small screens (phones, less than 768px) */
  @media (max-width: 767px) {
    .login {
      max-width: 90%; /* Button takes 90% of the screen width */
      height: 35px; /* Further reduced height */
      font-size: 12px; /* Smallest font size */
      padding: 8px 16px; /* Adjusted padding for smaller buttons */
    }
  }
  
  
  /* Media Queries for Responsiveness */
  @media (min-width: 768px) {
    .left-section,
    .right-section {
      width: 50%;
      padding: 40px;
    }
  
    .left-section-h1 {
      font-size: 48px;
    }
  
    .left-section-h1-span-level-1,
    .left-section-h1-span-assessment {
      font-size: 24px;
    }
  
    .left-section-p {
      font-size: 18px;
    }
  
    .right-section .login-button {
      font-size: 18px;
    }
  }
  
  @media (min-width: 1024px) {
    .left-section-h1 {
      font-size: 96px;
    }
  
    .left-section-h1-span-level-1,
    .left-section-h1-span-assessment {
      font-size: 48px;
    }
  
    .right-section .login-button {
      font-size: 16px;
    }
  
    .register-forgot {
      font-size: 16px;
    }
  }
  
  @media (max-width: 767px) {
    .divider {
      width: 1.5px;
      height: 30px;
      margin: 0 auto;
    }
  
    .right-section-automation {
      font-size: 14px;
      line-height: 22px;
      text-align: center;
    }
  
    .logo {
      max-width: 120px;
      width: 40%;
    }
  
    .register-forgot {
      font-size: 12px;
    }
  
    .right-section .login-button {
      font-size: 14px;
    }
  }
  