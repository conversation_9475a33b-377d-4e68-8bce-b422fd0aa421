import React, { useEffect, useState } from 'react';

import config from '../config';
import axios from 'axios';
import { useAuth } from 'react-oidc-context';
import { toast, ToastContainer } from 'react-toastify'; // Import toaster library
import 'react-toastify/dist/ReactToastify.css'; // Toaster styles
import './AssessmentPage.css';
import { useNavigate } from 'react-router-dom';

interface Question {
  questionOrder: any;
  id: string; // `id` field mapped from `questionId`
  questionText: string;
  progress: number;
  notes?: string;
  description?: string;
}

interface QuestionsProps {
  questions: Question[];
  responses: any[]; // ✅ Add responses here
  onProgressChange: (questionId: string, value: number) => void;
  onDocumentationToggle: (questionId: string) => void;
  documentationProvided: { [key: string]: boolean };
  domainTitle: string;
  practice: string;
  refreshData: () => void;
  questionsLoading: boolean;
  refreshHeader: () => void;
}

const Questions: React.FC<QuestionsProps> = ({
  questions,
  responses, // ✅ Ensure this is included
  onProgressChange,
  onDocumentationToggle,
  documentationProvided,
  domainTitle,
  practice,
  refreshData,
  questionsLoading,
  refreshHeader, // ✅ Ensure refreshHeader is included
}) => {
  const auth = useAuth();
  const [authLoading, setAuthLoading] = useState(true); // Added auth loading state
  const [notes, setNotes] = useState<{ [key: string]: string }>({});
  const [showSave, setShowSave] = useState<{ [key: string]: boolean }>({});
  const navigate = useNavigate();

  useEffect(() => {
    // Initialize notes state with existing question notes
    const initialNotes = questions.reduce(
      (acc, question) => {
        acc[question.id] = question.notes || ''; // Use existing notes or an empty string
        return acc;
      },
      {} as { [key: string]: string },
    );

    setNotes(initialNotes);
  }, [questions]);

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      const token = auth.user.access_token;

      // Optional: Check if the token has expired
      if (isTokenExpired(token)) {
        console.warn('Token has expired. Redirecting to login.');
        auth.signinRedirect(); // Redirect to login
        return null;
      }

      return token;
    }
    console.warn('User is not authenticated. Cannot fetch token.');
    return null;
  };

  const isTokenExpired = (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT token payload
      return payload.exp * 1000 < Date.now(); // Compare expiry with current time
    } catch (error) {
      console.error('Error decoding token:', error);
      return true;
    }
  };

  useEffect(() => {
    // Wait for authentication context to be ready
    if (!auth.isLoading) {
      setAuthLoading(false);
    }
  }, [auth.isLoading]);

  const handleNotesChange = (questionId: string, value: string) => {
    setNotes((prev) => ({
      ...prev,
      [questionId]: value,
    }));

    // Ensure save button appears even for clearing notes
    setShowSave((prev) => ({
      ...prev,
      [questionId]: value.trim() !== '' || notes[questionId] !== '',
    }));
  };

  const handleProgressChange = async (questionId: string, value: number) => {
    // ✅ Ensure the value is within the expected range (0, 25, 50, 75, 100)
    if (![0, 25, 50, 75, 100].includes(value)) {
      console.warn(`⚠️ Invalid progress value received: ${value}`);
      return;
    }

    // ✅ Update UI optimistically before API call
    onProgressChange(questionId, value);

    const token = await getAccessToken();
    if (!token) {
      console.error('❌ No access token available. Aborting API request.');
      return;
    }

    try {
      const response = await axios.post(
        `${config.userService.saveQuestionsProgress}`,
        {
          questionId,
          answer: value.toString(), // ✅ Convert value to string for consistency
          documentationProvided: documentationProvided[questionId] || false,
          notes: notes[questionId] || '', // ✅ Ensure notes are sent correctly
        },
        {
          headers: {
            accept: '*/*',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        },
      );

      toast.success('Progress saved successfully!');
      refreshHeader(); // 🔄 Refresh Header when toast is called
      refreshData();
    } catch (error: any) {
      console.error('❌ Error with POST request:', error.response || error);

      // ✅ Revert progress to previous value if API call fails
      onProgressChange(
        questionId,
        questions.find((q) => q.id === questionId)?.progress || 0,
      );
      toast.error('Failed to save progress. Please try again.');
    }
  };

  const handleCheckboxChange = async (questionId: string) => {
    try {
      const updatedState = !documentationProvided[questionId];
      const token = await getAccessToken();
      if (!token) return;

      const currentQuestion = questions.find((q) => q.id === questionId);
      const progressValue = currentQuestion?.progress || 0;

      await axios.post(
        `${config.userService.saveQuestionsProgress}`,
        {
          questionId,
          answer: progressValue.toString(),
          documentationProvided: updatedState,
          notes: notes[questionId] || '', // Ensure the correct notes value is sent
        },
        {
          headers: {
            accept: '*/*',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        },
      );

      onDocumentationToggle(questionId);
      refreshData();
    } catch (error: any) {
      console.error(
        `Error updating documentation state for question ${questionId}:`,
        error,
      );
      toast.error('Failed to update documentation state.');
    }
  };

  const handleSave = async (
    questionId: string,
    progress: number,
    note: string,
  ) => {
    try {
      const token = await getAccessToken();
      if (!token) return;

      const currentQuestion = questions.find((q) => q.id === questionId);
      const progressValue = progress || currentQuestion?.progress || 0;

      await axios.post(
        `${config.userService.saveQuestionsProgress}`,
        {
          questionId,
          answer: progressValue.toString(),
          documentationProvided: documentationProvided[questionId] || false,
          notes: note.trim(), // Ensure an empty string is sent properly
        },
        {
          headers: {
            accept: '*/*',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        },
      );

      toast.success('Notes saved successfully!');
      refreshData();
      refreshHeader(); // 🔄 Refresh Header when toast is called

      // Ensure Save button hides after saving even for empty notes
      setShowSave((prev) => ({ ...prev, [questionId]: false }));
    } catch (error) {
      console.error(`Error saving data for question ${questionId}:`, error);
      toast.error('Failed to save notes. Please try again.');
    }
  };

  return (
    <div>
      <ToastContainer
        position="top-center" // Use "bottom-center" for bottom-middle placement
        autoClose={3000} // Auto close after 3 seconds
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      <div className="mt-4 mb-4 p-4 border border-[rgb(221,221,221)] rounded-lg bg-blue-50">
        <h2 className="text-lg font-semibold text-blue-600 mb-4">
          Instructions
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-black text-sm">
          <li>
            Click on the <b>progress bar (0-100%)</b> to update your response.
          </li>
          <li>
            <b>0% = Not Met</b>, <b>1-99% = Partially Met</b>,{' '}
            <b>100% = Fully Met</b>.
          </li>
          <li>
            Ensure that you <b>select a valid answer</b> for each question
            before proceeding.
          </li>
          <li>
            Your responses are <b>automatically saved</b> after selection.
          </li>
          <li>
            If applicable, <b>add notes</b> where required.
          </li>
          <li>
            Questions marked as <b>"Not Answered"</b> need a response.{' '}
            <b>"Answered"</b> status appears when progress is updated.
          </li>
        </ul>
      </div>

      <p className="q-heading mb-4 text-lg sm:text-xl md:text-2xl lg:text-3xl">
        Questions
      </p>

      {questions.length > 0 ? (
        questions.map((question) => (
          <div
            key={question.id}
            className="mb-4 p-4 rounded-lg bg-white shadow-md flex flex-col gap-4"
          >
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <h6 className="q-text flex items-center gap-2">
                <span className="flex items-center justify-center w-8 h-8">
                  {responses?.some((res) => res.questionId === question.id) ? (
                    // ✅ Answered (Green Circle with Right Tick)
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 36 36"
                      fill="none"
                      className="w-full h-full"
                    >
                      <circle cx="18" cy="18" r="18" fill="#5ECA98" />
                      <path
                        d="M9 18L15 24L27 12"
                        stroke="white"
                        strokeWidth="3"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ) : (
                    // ❌ Not Answered (Red Circle with Exclamation Mark)
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 36 36"
                      fill="none"
                      className="w-full h-full"
                    >
                      <circle cx="18" cy="18" r="18" fill="#FE5C77" />
                      <line
                        x1="18"
                        y1="10"
                        x2="18"
                        y2="22"
                        stroke="white"
                        strokeWidth="3"
                        strokeLinecap="round"
                      />
                      <circle cx="18" cy="26" r="2" fill="white" />
                    </svg>
                  )}
                </span>

                <span className="flex-1">
                  {question.questionOrder}. {question.questionText}
                </span>

                {/* <Tooltip
                  title={question.description || 'Additional info'}
                  arrow
                >
                  <InfoIcon className="ml-2 cursor-pointer" fontSize="small" />
                </Tooltip> */}
              </h6>

              <div className="flex items-center gap-4">
                <input
                  type="range"
                  min={0}
                  max={100}
                  step={25}
                  value={question.progress ?? 0}
                  onChange={(e) => {
                    const newValue = parseInt(e.target.value, 10);

                    onProgressChange(question.id, newValue); // Update UI state immediately
                  }}
                  onMouseUp={(e) => {
                    const newValue = parseInt(
                      (e.target as HTMLInputElement).value,
                      10,
                    );

                    handleProgressChange(question.id, newValue);
                  }}
                  onTouchEnd={(e) => {
                    const newValue = parseInt(
                      (e.target as HTMLInputElement).value,
                      10,
                    );
                    handleProgressChange(question.id, newValue);
                  }}
                  className="progress-bar"
                />

                {/* ✅ Corrected logic for showing "Not Answered" */}
                <button
                  className={`answer-button px-4 py-2 rounded-md text-base sm:text-lg md:text-xl ${
                    responses?.some((res) => res.questionId === question.id)
                      ? ''
                      : 'not-answered'
                  }`}
                >
                  {responses?.some((res) => res.questionId === question.id)
                    ? `Answered (${question.progress ?? 0}%)`
                    : 'Not Answered'}
                </button>
              </div>
            </div>

            <button
              onClick={() => navigate('/preview-users')}
              className="w-fit inline-flex items-center text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 rounded px-4 py-1.5 transition duration-150 ease-in-out"
            >
              Preview Users
            </button>

            <div className="flex items-center mt-2">
              <input
                type="checkbox"
                className="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                checked={documentationProvided[question.id] || false}
                onChange={() => handleCheckboxChange(question.id)}
              />

              <span>I can provide documentation for the above control?</span>
            </div>

            <div className="flex items-center gap-2">
              <textarea
                className="flex-grow p-2 border rounded-md focus:ring focus:ring-blue-300"
                rows={3}
                value={
                  notes[question.id] !== undefined
                    ? notes[question.id]
                    : question.notes || ''
                }
                onChange={(e) => handleNotesChange(question.id, e.target.value)}
                placeholder="Enter additional notes here..."
              />

              {showSave[question.id] && (
                <button
                  className="
                    bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm 
                    w-16 sm:w-20 md:w-24 lg:w-28 xl:w-32 
                    p-2 text-center"
                  onClick={() =>
                    handleSave(
                      question.id,
                      question.progress,
                      notes[question.id] || '',
                    )
                  }
                >
                  Save
                </button>
              )}
            </div>
          </div>
        ))
      ) : (
        <p>No questions available.</p>
      )}
    </div>
  );
};

export default Questions;
