import PaymentHistory from "./pages/PaymentHistory";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173';

const config = {
  userService: {
    // Instructions Page APIs
    welcome: `${API_BASE_URL}${import.meta.env.VITE_API_WELCOME}`,
    startAssessment: `${API_BASE_URL}${import.meta.env.VITE_API_START_ASSESSMENT}`,

    // QuestionApp Page APIs
    levels: `${API_BASE_URL}${import.meta.env.VITE_API_LEVELS}`,
    levelQuestions: `${API_BASE_URL}${import.meta.env.VITE_API_LEVEL_QUESTIONS}`,
    progress: `${API_BASE_URL}${import.meta.env.VITE_API_PROGRESS}`,
    updateProgress: `${API_BASE_URL}${import.meta.env.VITE_API_UPDATE_PROGRESS}`,

     // Dashboard Page APIs
     domainsOverallProgress:  `${API_BASE_URL}${import.meta.env.VITE_API_OVERALL_PROGRESS}`,
     domainsProgress:  `${API_BASE_URL}${import.meta.env.VITE_API_DOMAINS_PROGRESS}`,
     barChart:  `${API_BASE_URL}${import.meta.env.VITE_API_BAR_CHART}`,

     // ASSESSMENT Page APIs
     domainsPractices:  `${API_BASE_URL}${import.meta.env.VITE_API_DOMAINS_PRACTICE}`,
     domainsQuestions:  `${API_BASE_URL}${import.meta.env.VITE_API_DOMAINS_QUESTIONS}`,
     questionsProgress:  `${API_BASE_URL}${import.meta.env.VITE_API_QUESTIONS_PROGRESS}`,
     reportPdf:  `${API_BASE_URL}${import.meta.env.VITE_API_REPORT_PDF}`,
     saveQuestionsProgress:  `${API_BASE_URL}${import.meta.env.VITE_API_SAVE_QUESTIONS_PROGRESS}`,

     // PAYMENT Page APIs
     checkoutSession:  `${API_BASE_URL}${import.meta.env.VITE_API_CHECKOUT_SESSION}`,
     paymentStatus:  `${API_BASE_URL}${import.meta.env.VITE_API_PAYMENT_STATUS}`,
     paymentCancelled:  `${API_BASE_URL}${import.meta.env.VITE_API_PAYMENT_CANCEL}`,
     accessCheck:  `${API_BASE_URL}${import.meta.env.VITE_API_ACCESS_CHECK}`,
     paymentHistory:  `${API_BASE_URL}${import.meta.env.VITE_API_PAYMENT_HISTORY}`,

     
     //USER AGREEMENT
     UserAgreement:  `${API_BASE_URL}${import.meta.env.VITE_API_USER_AGREEMENT}`,


     // COMPANY Page APIs
     orgDetails:  `${API_BASE_URL}${import.meta.env.VITE_API_ORG_DETAILS}`,

     // USER PROFILE Page APIs
     userDetails:  `${API_BASE_URL}${import.meta.env.VITE_API_USER_DETAILS}`,
  },
cognito: {
  authority: import.meta.env.VITE_COGNITO_AUTHORITY,
  clientId: import.meta.env.VITE_COGNITO_CLIENT_ID,
  clientSecret: import.meta.env.VITE_COGNITO_CLIENT_SECRET,
  redirectUri: import.meta.env.VITE_COGNITO_REDIRECT_URI,
  signoutUri: import.meta.env.VITE_COGNITO_SIGNOUT_URI,
  cognitoDomain: import.meta.env.VITE_COGNITO_DOMAIN,
  responseType: import.meta.env.VITE_COGNITO_RESPONSE_TYPE,
  scope: import.meta.env.VITE_COGNITO_SCOPE,
},

  analytics: {
    googleAnalyticsId: import.meta.env.VITE_GOOGLE_ANALYTICS_ID, // Google Analytics ID
  },
};

export default config;
