import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import DefaultLayout from "../layout/DefaultLayout";
import { useAuth } from "react-oidc-context";
import config from "../config";
import ThumbUpAltIcon from "@mui/icons-material/ThumbUpAlt";
import "./PaymentPage.css";

const PaymentPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const auth = useAuth();
  const [status, setStatus] = useState<"success" | "initial" | null>(null);

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    console.log("User is not authenticated. Cannot fetch token.");
    return null;
  };

 

  const checkAccess = async () => {
    if (!auth.isAuthenticated || !auth.user) {
      console.error("User is not authenticated yet.");
      return;
    }

    try {
      const token = await getAccessToken();
      if (!token) {
        // navigate('/login');
      console.log('not authenticated')
        // return;
      }

      const response = await fetch(`${config.userService.accessCheck}`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          accept: "*/*",
        },
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json(); // ✅ Parse JSON response
      console.log("Access check response:", data);

      // ✅ Explicitly check if accessGranted is false
      if (data.accessGranted === true) {
        setStatus("success"); // Show success UI instead of redirecting
      } else {
        setStatus("initial"); // Show success UI instead of redirecting
      }
      
    } catch (error) {
      console.error("Access check failed:", error);
    
    }
  };

 

  useEffect(() => {
    const authenticate = async () => {
      try {
        if (!auth.isAuthenticated) {
          await auth.signinSilent(); // Attempt silent authentication
        }
        if (auth.isAuthenticated && auth.user) {
          checkAccess();
        }
      } catch (error) {
        console.error("Silent sign-in failed:", error);
      }
    };
  
    authenticate();
  }, [auth.isAuthenticated, auth.user]); // Depend on auth state
  
 
  
  
  
  

  const handleCheckout = async () => {
    const token = await getAccessToken();
    if (!token) {
      console.log("Failed to retrieve access token.");
      return;
    }
    try {
      const response = await fetch(`${config.userService.checkoutSession}`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          accept: "*/*",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to create checkout session: ${response.status} - ${response.statusText}`);
      }

      const { url } = await response.json();

      if (url) {
        window.location.href = url;
      } else {
        console.log("Checkout session URL was not provided by the API.");
      }
    } catch (error: any) {
      console.log(`Error during checkout session creation: ${error.message}`);
    }
  };

  const handleStartAssessment = async () => {
    const token = await getAccessToken();
    try {
      const response = await fetch(`${config.userService.startAssessment}`, {
        method: 'POST',
        headers: {
          accept: '*/*',
          Authorization: `Bearer ${token}`,
        },
        body: '', // Empty body as per your curl command
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      console.log('Assessment started successfully.');
      navigate('/dashboard'); // Navigate only after a successful API response
    } catch (error) {
      console.error('Error starting assessment:', error);
    }
  };

  return (
    <DefaultLayout>
      <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md text-center">

      
      
        {errorMessage && (
          <div className="bg-red-100 text-red-700 p-4 rounded-lg mb-4">
            <p>{errorMessage}</p>
          </div>
        )}

        {/* Initial page - Show Proceed to Payment */}
        {status === "initial" && (
          <>
            <h1 className="heading text-2xl font-bold mb-4">Secure Payment</h1>
            <p className="para mb-6">
           Please start your CMMC level 1 assessment by making a one time secure payment.</p>


            <button
              onClick={handleCheckout}
              className="w-full text-white font-medium py-3 rounded-lg transition duration-200 blue-btn"
            >
              Proceed to Payment
            </button>
          </>
        )}

        {/* Payment Success Page */}
        {status === "success" && (
          <>
            <div className="flex flex-col items-center justify-center text-center space-y-4">
              <div className="mx-auto flex items-center justify-center w-24 h-24 rounded-full bg-blue-100">
                <ThumbUpAltIcon
                  style={{ fontSize: 60, color: "rgb(0, 37, 116, var(--tw-text-opacity))" }}
                />
              </div>

              <h1 className="heading text-3xl font-bold text-gray-800">You're All Set!</h1>
              {/* <p className="para text-gray-600">
                Thank you for signing up! The payment invoice has been sent to your registered email.
              </p> */}
              {/* <p className="mail-text text-lg font-bold text-gray-800"><EMAIL></p> */}
              <p className="para text-gray-600">
                You now have full access to the CMMC Level 1 Assessment. Start answering the questions
                to assess your compliance readiness.
              </p>

              <button
                className="relative flex items-center text-white px-6 py-3 rounded-lg shadow-md transition-all blue-btn"
                onClick={handleStartAssessment}
              >
                Start Assessment
              </button>

              
            </div>
          </>
        )}

      
      </div>
    </DefaultLayout>
  );
};

export default PaymentPage;
