import React from 'react';

interface Question {
  id: number;
  text: string;
  progress: number;
  questionOrder: number; // Add this property to the Question interface
}

interface VerticalProgressBarProps {
  questions: Question[];
}

const VerticalProgressBar: React.FC<VerticalProgressBarProps> = ({
  questions,
}) => {
  return (
    <div className="w-10 bg-gray-200 rounded-lg h-[80vh] mx-auto flex flex-col items-center pt-4 pb-4">
      {questions.map((question, index) => (
        <div
          key={question.id}
          className="flex flex-col items-center w-full mb-12 last:mb-0"
        >
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
              question.progress > 0 ? 'bg-green-500 text-white' : 'bg-gray-300'
            }`}
          >
            {question.progress > 0 ? '✔' : question.questionOrder} {/* Display questionOrder */}
          </div>
          {index < questions.length - 1 && (
            <div
              className={`w-1 ${
                question.progress > 0 ? 'bg-green-500' : 'bg-gray-300'
              }`}
              style={{ height: '50px' }} // Adjust spacing here
            ></div>
          )}
        </div>
      ))}
    </div>
  );
};

export default VerticalProgressBar;
