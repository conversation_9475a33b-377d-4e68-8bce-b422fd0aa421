import { useEffect } from 'react';
import { useAuth } from 'react-oidc-context';

const INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes in milliseconds

const SessionTimeoutHandler = () => {
  const auth = useAuth();

  useEffect(() => {
    if (!auth.isAuthenticated) return;

    let inactivityTimer: ReturnType<typeof setTimeout>;
    
    const resetTimer = () => {
      clearTimeout(inactivityTimer);
      inactivityTimer = setTimeout(handleTimeout, INACTIVITY_TIMEOUT);
    };

    const handleTimeout = () => {
      console.log("Session timeout due to inactivity");
      sessionStorage.setItem('loggingOut', 'true');
      auth.removeUser();
      window.location.replace('/assessment');
    };

    // Check if token is expired on component mount
    const checkTokenExpiration = () => {
      if (!auth.user?.access_token) return;
      
      try {
        const payload = JSON.parse(atob(auth.user.access_token.split('.')[1]));
        if (payload.exp * 1000 < Date.now()) {
          handleTimeout();
        }
      } catch (error) {
        console.error('Error checking token expiration:', error);
        handleTimeout();
      }
    };

    // Set up event listeners for user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => document.addEventListener(event, resetTimer));
    
    // Initialize timer and check token
    resetTimer();
    checkTokenExpiration();

    // Set up periodic token check
    const tokenCheckInterval = setInterval(checkTokenExpiration, 60000); // Check every minute

    return () => {
      clearTimeout(inactivityTimer);
      clearInterval(tokenCheckInterval);
      events.forEach(event => document.removeEventListener(event, resetTimer));
    };
  }, [auth.isAuthenticated, auth.user]);

  return null;
};

export default SessionTimeoutHandler;
