import React, { useState } from 'react';


interface Question {
  id: number;
  question: string;
  options: string[];
  answer: string;
}

interface QuestionProps {
  questions: Question[];
  onComplete: () => void;
}

export const Question: React.FC<QuestionProps> = ({ questions, onComplete }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);

  const currentQuestion = questions[currentQuestionIndex];

  const handleAnswerSelect = (option: string) => {
    setSelectedAnswer(option);
    setIsCorrect(option === currentQuestion.answer);
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer('');
    } else {
      onComplete();
    }
  };

  return (
   
    <div>
      <div className="mb-4">
        <h3 className="text-xl font-medium">{currentQuestion.question}</h3>
        <div className="mt-4 space-y-2">
          {currentQuestion.options.map((option) => (
            <button
              key={option}
              onClick={() => handleAnswerSelect(option)}
              className={`block w-full py-2 px-4 text-left rounded-lg border ${
                selectedAnswer === option
                  ? isCorrect
                    ? 'bg-green-200 border-green-400'
                    : 'bg-red-200 border-red-400'
                  : 'bg-gray-100 border-gray-300'
              }`}
            >
              {option}
            </button>
          ))}
        </div>
      </div>
      {selectedAnswer && (
        <button
          onClick={handleNextQuestion}
          className="mt-4 bg-blue-500 text-white py-2 px-6 rounded-lg hover:bg-blue-600"
        >
          {currentQuestionIndex < questions.length - 1 ? 'Next' : 'Finish Level'}
        </button>
      )}
    </div>
    
  );
};
