import React, { useEffect, useState } from 'react';
import './UserProfile.css';
import {
  Avatar,
  Button,
  TextField,
  Card,
  CardContent,
  IconButton,
  SelectChangeEvent,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CloseIcon from '@mui/icons-material/Close';
import DefaultLayout from '../layout/DefaultLayout';
import { useAuth } from 'react-oidc-context';
import config from '../config';
import { useNavigate } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import { MenuItem, Select, FormControl, InputLabel } from '@mui/material';

const UserProfile: React.FC = () => {
  // State for form fields
  const [profile, setProfile] = useState({
    firstName: '',
    lastName: '',
    email: '',
    companyName: '',
    industryType: '',
    companySize: '',
    role: '',
    complianceGoals: '',
    cmmcReadinessStage: '',
  });
  const auth = useAuth();

  // State to track edit mode
  const [isEditingPersonal, setIsEditingPersonal] = useState(false);
  const [isEditingOrganization, setIsEditingOrganization] = useState(false);

  const [editedProfile, setEditedProfile] = useState(profile);
  const [avatar, setAvatar] = useState<string>(''); // Default image
  const navigate = useNavigate();

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    console.error('User is not authenticated. Cannot fetch token.');
    return null;
  };

  const fetchUserProfile = async () => {
    const token = await getAccessToken();
    if (!token) {
      console.error('No token available. Aborting API call.');
      return;
    }

    try {
      const response = await fetch(`${config.userService.userDetails}`, {
        method: 'GET',
        headers: {
          Accept: '*/*',
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok)
        throw new Error(`Error fetching user profile: ${response.status}`);

      const userData = await response.json();

      setProfile((prevProfile) => ({
        ...prevProfile,
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
      }));
    } catch (error) {
      console.error(error);
    }
  };

  const fetchOrgDetails = async () => {
    const token = await getAccessToken();
    if (!token) {
      console.error('No token available. Aborting API call.');
      return;
    }

    try {
      const response = await fetch(`${config.userService.orgDetails}`, {
        method: 'GET',
        headers: {
          Accept: '*/*',
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok)
        throw new Error(`Error fetching org details: ${response.status}`);

      const orgData = await response.json();

      setProfile((prevProfile) => ({
        ...prevProfile,
        companyName: orgData.companyName || '',
        industryType: orgData.industryType || '',
        companySize: orgData.companySize || '',
        role: orgData.role || '',
        complianceGoals: orgData.complianceGoals || '',
        cmmcReadinessStage: orgData.cmmcReadinessStage || '',
      }));
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        await fetchUserProfile();
        await fetchOrgDetails();
      } catch (error) {
        console.error('Failed to fetch user data:', error);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    setEditedProfile(profile);
  }, [profile]);

  // Handle Input Change
  const handleInputChange = (
    e:
      | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | SelectChangeEvent<string>,
  ) => {
    const { name, value } = e.target as { name: string; value: string }; // ✅ Ensures compatibility
    setEditedProfile((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle Save for a specific section

  const handleSave = async (section: 'personal' | 'organization') => {
    const token = await getAccessToken();
    if (!token) {
      console.error('No token available. Aborting API call.');
      return;
    }

    let apiUrl = '';
    let requestBody = {};

    if (section === 'personal') {
      apiUrl = `${config.userService.userDetails}`;
      requestBody = {
        firstName: editedProfile.firstName,
        lastName: editedProfile.lastName,
      };
    } else if (section === 'organization') {
      apiUrl = `${config.userService.orgDetails}`;
      requestBody = {
        companyName: editedProfile.companyName,
        industryType: editedProfile.industryType,
        companySize: editedProfile.companySize,
        role: editedProfile.role,
        complianceGoals: editedProfile.complianceGoals,
        cmmcReadinessStage: editedProfile.cmmcReadinessStage,
      };
    }

    try {
      const response = await fetch(apiUrl, {
        method: 'PUT',
        headers: {
          Accept: '*/*',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      const responseText = await response.text();
      if (!response.ok) {
        toast.error(responseText || 'Error updating details.');
        throw new Error(responseText);
      }

      setProfile((prev) => ({
        ...prev,
        ...requestBody,
      }));

      section === 'personal'
        ? setIsEditingPersonal(false)
        : setIsEditingOrganization(false);
      toast.success(
        `${
          section === 'personal' ? 'Personal' : 'Organization'
        } details updated successfully!`,
      );
    } catch (error) {
      console.error(`Failed to update ${section} details:`, error);
    }
  };

  // Handle Cancel for a specific section
  const handleCancel = (section: 'personal' | 'organization') => {
    setEditedProfile(profile);
    if (section === 'personal') {
      setIsEditingPersonal(false);
    } else {
      setIsEditingOrganization(false);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();

      reader.onload = () => {
        if (reader.result) {
          setAvatar(reader.result.toString()); // Update avatar state with image preview
        }
      };

      reader.readAsDataURL(file); // Convert image to Base64 URL
    }
  };
  return (
    <DefaultLayout>
      <ToastContainer
        position="top-center" // Use "bottom-center" for bottom-middle placement
        autoClose={3000} // Auto close after 3 seconds
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      <div className="bg-gray-100 min-h-screen flex flex-col items-center p-6">
        <div
          className="mb-4"
          style={{
            display: 'flex',
            justifyContent: 'flex-start',
            width: '100%',
          }}
        >
          <Button
            variant="contained"
            color="secondary"
            onClick={() => navigate('/dashboard')}
            sx={{
              fontWeight: 'bold',
              fontSize: {
                xs: '12px', // Smallest screens
                sm: '14px', // Small screens (default)
                md: '16px', // Medium screens
                lg: '18px', // Large screens
              },
              textTransform: 'none',
              px: 3,
              py: 1.5,
              boxShadow: 2,
              backgroundColor: '#1976d2',
              '&:hover': {
                backgroundColor: '#155fa0',
              },
              mr: 2,
            }}
          >
            Dashboard
          </Button>
        </div>

        {/* Profile Section */}
        {/* <div className="w-full max-w-6xl bg-white shadow-md rounded-lg p-6">
          <h2 className="text-2xl profile-heading heading-common mb-4">
            My Profile
          </h2>

          <div className="flex items-center space-x-6">
            {/* Profile Avatar */}
        {/* <Avatar src={avatar} sx={{ width: 100, height: 100 }} /> */}

        {/* <div>
               Use <label> for file input 
               <label htmlFor="file-upload" className="btn p-4 cursor-pointer">
                Upload new photo
              </label>
              <input
                id="file-upload"
                type="file"
                className="hidden"
                accept="image/*"
                onChange={handleImageChange} // Call function on file select
              />

              <p className="text-xs text-gray-500 mt-3">
                At least 800x800 px recommended. JPG or PNG is allowed
              </p>
            </div> */}
        {/* </div> */}
        {/* </div> */}

        {/* Personal Information Section */}
        <Card className="w-full max-w-6xl mt-6 shadow-md">
          <CardContent>
            <div className="flex justify-between items-center">
              <h3 className="text-lg section-heading heading-common">
                Personal Information
              </h3>
              {isEditingPersonal ? (
                <div>
                  <IconButton onClick={() => handleSave('personal')}>
                    <SaveIcon color="primary" />
                  </IconButton>
                  <IconButton onClick={() => handleCancel('personal')}>
                    <CloseIcon color="error" />
                  </IconButton>
                </div>
              ) : (
                <IconButton
                  onClick={() => {
                    setEditedProfile(profile);
                    setIsEditingPersonal(true);
                  }}
                >
                  <EditIcon />
                </IconButton>
              )}
            </div>
            <div className="mt-4 grid grid-cols-2 gap-4">
              <TextField
                label={
                  <span>
                    First Name <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                name="firstName"
                value={
                  isEditingPersonal
                    ? editedProfile.firstName
                    : profile.firstName || ''
                }
                onChange={handleInputChange}
                fullWidth
                variant="outlined"
                disabled={!isEditingPersonal}
              />
              <TextField
                label={
                  <span>
                    Last Name <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                name="lastName"
                value={
                  isEditingPersonal
                    ? editedProfile.lastName
                    : profile.lastName || ''
                }
                onChange={handleInputChange}
                fullWidth
                variant="outlined"
                disabled={!isEditingPersonal}
              />
              <TextField
                label="Email"
                name="email"
                value={profile.email || ''}
                fullWidth
                variant="outlined"
                disabled={true}
              />
            </div>
          </CardContent>
        </Card>

        {/* Organization & CMMC Readiness Overview */}
        <Card className="w-full max-w-6xl mt-6 shadow-md">
          <CardContent>
            <div className="flex justify-between items-center">
              <h3 className="text-lg section-heading heading-common">
                Organization & CMMC Readiness Overview
              </h3>
              {isEditingOrganization ? (
                <div>
                  <IconButton onClick={() => handleSave('organization')}>
                    <SaveIcon color="primary" />
                  </IconButton>
                  <IconButton onClick={() => handleCancel('organization')}>
                    <CloseIcon color="error" />
                  </IconButton>
                </div>
              ) : (
                <IconButton
                  onClick={() => {
                    setEditedProfile(profile);
                    setIsEditingOrganization(true);
                  }}
                >
                  <EditIcon />
                </IconButton>
              )}
            </div>
            <div className="mt-4 grid grid-cols-2 gap-4">
              <TextField
                label={
                  <span>
                    Organization <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                name="companyName"
                value={
                  isEditingOrganization
                    ? editedProfile.companyName
                    : profile.companyName || ''
                }
                onChange={handleInputChange}
                fullWidth
                variant="outlined"
                disabled={!isEditingOrganization}
              />

              <FormControl
                fullWidth
                variant="outlined"
                disabled={!isEditingOrganization}
              >
                <InputLabel>
                  <span>
                    Industry Type <span style={{ color: 'red' }}>*</span>
                  </span>
                </InputLabel>
                <Select
                  name="industryType"
                  value={
                    isEditingOrganization
                      ? editedProfile.industryType
                      : profile.industryType || ''
                  }
                  onChange={handleInputChange}
                  label="Industry Type"
                >
                  {[
                    'Defense Contractor',
                    'IT Services',
                    'Manufacturing',
                    'Healthcare',
                    'Financial Services',
                  ].map((option) => (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl
                fullWidth
                variant="outlined"
                disabled={!isEditingOrganization}
              >
                <InputLabel>
                  <span>
                    Organization Size <span style={{ color: 'red' }}>*</span>
                  </span>
                </InputLabel>
                <Select
                  name="companySize"
                  value={
                    isEditingOrganization
                      ? editedProfile.companySize
                      : profile.companySize || ''
                  }
                  onChange={handleInputChange}
                  label="Organization Size"
                >
                  {['1-50 employees', '51-500 employees', '501+ employees'].map(
                    (option) => (
                      <MenuItem key={option} value={option}>
                        {option}
                      </MenuItem>
                    ),
                  )}
                </Select>
              </FormControl>
              <TextField
                label={
                  <span>
                    Role <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                name="role"
                value={
                  isEditingOrganization
                    ? editedProfile.role
                    : profile.role || ''
                }
                onChange={handleInputChange}
                fullWidth
                variant="outlined"
                disabled={!isEditingOrganization}
              />
              <FormControl
                fullWidth
                variant="outlined"
                disabled={!isEditingOrganization}
              >
                <InputLabel>
                  <span>
                    Compliance Goals <span style={{ color: 'red' }}>*</span>
                  </span>
                </InputLabel>
                <Select
                  name="complianceGoals"
                  value={
                    isEditingOrganization
                      ? editedProfile.complianceGoals
                      : profile.complianceGoals || ''
                  }
                  onChange={handleInputChange}
                  label="Compliance Goals"
                >
                  <MenuItem value="">Select compliance goals</MenuItem>
                  {[
                    'Gain or retain DoD contracts',
                    'Meet regulatory requirements',
                    'Enhance security posture',
                    'All of the above',
                  ].map((option) => (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl
                fullWidth
                variant="outlined"
                disabled={!isEditingOrganization}
              >
                <InputLabel>
                  <span>
                    CMMC Readiness Stage <span style={{ color: 'red' }}>*</span>
                  </span>
                </InputLabel>
                <Select
                  name="cmmcReadinessStage"
                  value={
                    isEditingOrganization
                      ? editedProfile.cmmcReadinessStage
                      : profile.cmmcReadinessStage || ''
                  }
                  onChange={handleInputChange}
                  label="CMMC Readiness Stage"
                >
                  {[
                    'Just Starting',
                    'Somewhat Prepared',
                    'Actively Preparing for Certification',
                    'Ready for Self-Certification',
                  ].map((option) => (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>
          </CardContent>
        </Card>
      </div>
    </DefaultLayout>
  );
};

export default UserProfile;
