import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "react-oidc-context";

const ProtectedRoute = ({ children }: { children: JSX.Element }) => {
  const auth = useAuth();
  const location = useLocation();

  // Check for logout in progress
  const isLoggingOut = sessionStorage.getItem('loggingOut') === 'true';
  if (isLoggingOut) {
    console.log("🔄 Logout in progress, redirecting to home...");
    sessionStorage.removeItem('loggingOut');
    window.location.replace('/');
    return null;
  }

  // Show loading while auth is being determined
  if (auth.isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
        <p className="mt-4 text-lg text-gray-700">Authenticating... Please wait</p>
      </div>
    );
  }

  // If not authenticated, redirect to home
  if (!auth.isAuthenticated) {
    // If we're in the middle of a callback, show loading
    if (window.location.search.includes('code=')) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
          <p className="mt-4 text-lg text-gray-700">Processing authentication... Please wait</p>
        </div>
      );
    }

    // Otherwise redirect to home
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // User is authenticated, render the protected content
  return children;
};

export default ProtectedRoute;
