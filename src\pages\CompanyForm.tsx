import React, { useState, useEffect } from "react";
import DefaultLayout from "../layout/DefaultLayout";
import { useAuth } from "react-oidc-context";
import config from "../config";
import "./CompanyForm.css";
import { useNavigate } from "react-router-dom";
import { toast, ToastContainer } from 'react-toastify';

type OrganizationDataType = {
  id: string;
  userId: string;
  companyName: string;
  industryType: string;
  companySize: string;
  businessType: string;
  role: string;
  complianceGoals: string;
  cmmcReadinessStage: string;
};

const CompanyForm: React.FC = () => {
  const auth = useAuth();
  
  const [organizationData, setOrganizationData] = useState<OrganizationDataType | null>(null);
  const [formData, setFormData] = useState<OrganizationDataType>({
    id: "",
    userId: "",
    companyName: "",
    industryType: "",
    companySize: "",
    businessType: "",
    role: "",
    complianceGoals: "",
    cmmcReadinessStage: "",
  });
  const requiredFields = ["companyName", "industryType", "businessType", "companySize", "role", "complianceGoals", "cmmcReadinessStage"];

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
   const navigate = useNavigate();

  // Fetch Access Token
  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    console.error("User is not authenticated. Cannot fetch token.");
    return null;
  };

  

  const fetchOrganizationDetails = async () => {
    const token = await getAccessToken();
    if (!token) return;
  
    try {
      console.log("Fetching organization details...");
  
      const response = await fetch(`${config.userService.orgDetails}`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          accept: "*/*",
        },
      });
  
      console.log("GET /organization response status:", response.status);
  
      // ✅ If response is not JSON, handle it as a plain text error
      const contentType = response.headers.get("content-type");
      if (!response.ok || !contentType || !contentType.includes("application/json")) {
        const textResponse = await response.text();
        console.log("GET API response text:", textResponse);
  
        if (textResponse.includes("Organization details not found")) {
          console.log("Organization details not found. Setting organizationData to null.");
          setOrganizationData(null);
          setIsEditing(false);
          return;
        }
  
        throw new Error(`Unexpected API response: ${textResponse}`);
      }
  
      // ✅ Parse JSON response
      const data = await response.json();
      console.log("GET API returned:", data);
  
      if (data && data.id) {
        console.log("Organization found. Using PUT.");
        setOrganizationData(data);
        setFormData(data);
        setIsEditing(true);
      } else {
        console.log("No organization data received. Using POST.");
        setOrganizationData(null);
        setIsEditing(false);
      }
    } catch (error) {
      console.error("Error fetching organization details:", error);
      setOrganizationData(null); // ✅ Ensure state resets even on error
      setIsEditing(false);
    } finally {
      setLoading(false);
    }
  };
  
  const checkAccess = async () => {
    if (!auth.isAuthenticated || !auth.user) return;
  
    try {
      const username =
        `${auth.user.profile.given_name || ''} ${auth.user.profile.family_name || ''}`.trim() || 'User';
      // Username available from auth.user, no need to store in localStorage
  
      const token = auth.user.access_token; // Get access token
      const response = await fetch(`${config.userService.accessCheck}`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          accept: "*/*",
        },
      });
  
      if (!response.ok) throw new Error(`Error: ${response.status}`);
  
      const data = await response.json(); // Parse API response
  
      // Navigate to /org if either access is denied or org_details_saved is false
      if (!data.accessGranted || data.org_details_saved === false) {
        return;
      } else {
        navigate("/dashboard", { replace: true });
      }
  
    } catch (error) {
      console.error("Access check failed:", error);
    }
  };
  
  // Fetch Organization Details (GET API)
  useEffect(() => {
   
    checkAccess();
    fetchOrganizationDetails();
  }, [auth]); // Added `auth` as dependency to avoid stale values

  // Handle form input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Validate form fields before submission
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    requiredFields.forEach((field) => {
      if (!formData[field as keyof typeof formData]) {
        newErrors[field] = `${field.replace(/([A-Z])/g, ' $1').trim()} is required`;
      }
    });
  
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  

  // Handle form submission (POST / PUT API)
 

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log("Form submitted!");
  
    const token = await getAccessToken();
    if (!token) {
      console.log("No token found. Aborting request.");
      toast.error("Authentication failed. Please login again.");
      return;
    }
  
    try {
      const apiUrl = `${config.userService.orgDetails}`;
      const method = organizationData === null ? "POST" : "PUT";
  
      console.log(`API Request Method: ${method}`);
      console.log("Request Payload:", JSON.stringify(formData));
  
      const response = await fetch(apiUrl, {
        method,
        headers: {
          Authorization: `Bearer ${token}`,
          accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });
  
      console.log(`Response Status (${method}):`, response.status);
  
      // ✅ Extract response text
      const responseText = await response.text();
  
      if (!response.ok) {
        toast.error(responseText || "Error updating details.");
        throw new Error(responseText);
      }
  
      // ✅ Successfully saved, show success toast
      toast.success("Organization details saved successfully!");
  
      const savedData = JSON.parse(responseText);
      setOrganizationData(savedData);
      setIsEditing(false);
      setErrors({});
  
      console.log("Organization details saved successfully.");
  
      navigate("/payment"); // ✅ Redirect to payment page after successful save
  
    } catch (error) {
      console.error("Error saving organization details:", error);
      
      setErrors({ general: "An unexpected error occurred. Please try again." });
    }
  };
  
  
  
  

  return (
    <DefaultLayout>
      <ToastContainer
              position="top-center" // Use "bottom-center" for bottom-middle placement
              autoClose={3000} // Auto close after 3 seconds
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
            />
      <div className="mx-auto bg-white shadow-md rounded-md p-6">
        <h1 className="form-heading text-2xl mb-4 text-center">Organization Information</h1>

        {loading ? (
          <p className="text-center">Loading...</p>
        ) : !isEditing && organizationData ? (
          // Show table when not editing
          <div>
            <h2 className="text-lg font-semibold mb-4">Existing Organization Details</h2>
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-200">
                  <th className="border p-2">Organization Name</th>
                  <th className="border p-2">Industry Type</th>
                  <th className="border p-2">Organization Size</th>
                  {/* <th className="border p-2">Business Type</th> */}
                  <th className="border p-2">Role</th>
                  <th className="border p-2">Compliance Goals</th>
                  <th className="border p-2">CMMC Readiness Stage</th>
                  <th className="border p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border p-2">{organizationData.companyName}</td>
                  <td className="border p-2">{organizationData.industryType}</td>
                  <td className="border p-2">{organizationData.companySize}</td>
                  {/* <td className="border p-2">{organizationData.businessType}</td> */}
                  <td className="border p-2">{organizationData.role}</td>
                  <td className="border p-2">{organizationData.complianceGoals}</td>
                  <td className="border p-2">{organizationData.cmmcReadinessStage}</td>
                  <td className="border p-2">
                    <button
                      className="bg-blue-500 text-white px-3 py-1 rounded-md"
                      onClick={() => setIsEditing(true)}
                    >
                      Edit
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        ) : (
          // Show form when editing or if no organization data
          <div>
          <h2 className="text-lg form-text mb-4">
            {isEditing ? "Edit Organization Details" : "No organization details found. Please add your organization."}
          </h2>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Company Information Section */}
            <div>
              <h2 className="form-text text-lg  mb-4">Tell Us About Your Organization</h2>
              <div className="space-y-4">
                {[
                  { id: "companyName", label: "Organization Name", type: "text" },
                  {
                    id: "industryType",
                    label: "Industry Type",
                    type: "select",
                    options: [
                      "Defense Contractor",
                      "IT Services",
                      "Manufacturing",
                      "Healthcare",
                      "Financial Services"
                    ],
                  },
                  
                  {
                    id: "companySize",
                    label: "Organization Size",
                    type: "select",
                    options: ["1-50 employees", "51-500 employees", "501+ employees"],
                  },
                  
                ].map(({ id, label, type, options }) => (
                  <div key={id}>
                   {/* Mandatory Fields */}

<label htmlFor={id} className="form-label text-sm font-medium">
  {label} {requiredFields.includes(id) && <span className="text-red-500">*</span>}
</label>

                     
                    {type === "select" ? (
                     <select
                     id={id}
                     name={id}
                     className={`w-full border p-2 rounded-md ${
                       isEditing && ["industryType", "businessType"].includes(id) ? "bg-gray-200" : ""
                     }`}
                     value={formData[id as keyof typeof formData]}
                     onChange={handleChange}
                    
                   >
                     <option value="">Select {label.toLowerCase()}</option>
                     {options?.map((option) => (
                       <option key={option} value={option}>
                         {option}
                       </option>
                     ))}
                   </select>
                   
                   
                    ) : (
                      <input
                      type="text"
                      id={id}
                      name={id}
                      placeholder={`Enter ${label.toLowerCase()}`}
                      className="w-full border p-2 rounded-md"
                      value={formData[id as keyof typeof formData]}
                      onChange={handleChange}
                     
                    />
                    
                    )}
                    {errors[id] && <p className="text-sm text-red-600">{errors[id]}</p>}
                  </div>
                ))}
              </div>
            </div>
      
            {/* CMMC Readiness and Role */}
            <div>
              <h2 className="form-heading text-2xl mb-4 text-center">CMMC Readiness and Role</h2>
              <div className="space-y-4">
                {/* Your Role Field */}
                <div>
                  <label htmlFor="role" className="form-label text-sm font-medium">
                    Your Role <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="role"
                    name="role"
                    placeholder="Enter your role"
                    className="w-full border border-gray-300 rounded-md p-2 focus:ring focus:ring-blue-300"
                    value={formData.role}
                    onChange={handleChange}
                  />
                  {errors.role && <p className="text-sm text-red-600">{errors.role}</p>}
                </div>
      
                {/* Compliance Goals Field */}
                <div>
                  <label htmlFor="complianceGoals" className="form-label text-sm font-medium">
                    Compliance Goals <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="complianceGoals"
                    name="complianceGoals"
                    className="w-full border border-gray-300 rounded-md p-2 focus:ring focus:ring-blue-300"
                    value={formData.complianceGoals}
                    onChange={handleChange}
                  >
                    <option value="">Select compliance goals</option>
                    <option value="Gain or retain DoD contracts">Gain or retain DoD contracts</option>
                    <option value="Meet regulatory requirements">Meet regulatory requirements</option>
                    <option value="Enhance security posture">Enhance security posture</option>
                    <option value="Enhance security posture">All of the above</option>
                    
                  </select>
                  {errors.complianceGoals && <p className="text-sm text-red-600">{errors.complianceGoals}</p>}
                </div>
              </div>
            </div>
      
            {/* CMMC Readiness Stage */}
            <div>
              <label className="form-label block text-sm font-medium text-gray-700">CMMC Readiness Stage <span className="text-red-500">*</span></label>
              <div className="space-y-2">
                {["Just Starting", "Somewhat Prepared", "Actively Preparing for Certification", "Ready for Self-Certification"].map(
                  (value) => (
                    <label key={value} className="form-label flex items-center">
                      <input
                        type="radio"
                        name="cmmcReadinessStage"
                        value={value}
                        className="mr-2"
                        checked={formData.cmmcReadinessStage === value}
                        onChange={handleChange}
                      />
                      {value}
                    </label>
                  )
                )}
              </div>
              {errors.cmmcReadinessStage && <p className="text-sm text-red-600">{errors.cmmcReadinessStage}</p>}
            </div>
      
            {/* Submit Button */}
            <div className="text-center">
              <button type="submit" className="px-6 py-2 bg-[#00C1A0] text-white font-semibold rounded-md">
                {isEditing ? "Next" : "Next"}
              </button>
            </div>
          </form>
        </div>
        )}
       
      </div>
    </DefaultLayout>
  );
};

export default CompanyForm;
