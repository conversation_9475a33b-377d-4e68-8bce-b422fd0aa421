/* Background */
.background-customizable {
  background: #fff;
  color: #333; /* Default text color */
}



/* Logo */
.logo-customizable {
  max-width: 50%;
  max-height: 50%;
}

/* Banner */
.banner-customizable {
  padding: 20px;
  background-color: white; 
  color: #000; /* Updated to black for better contrast */
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}

/* Form Labels */
.label-customizable {
  color: #002B76 !important; 
  
  font-size: 16px;
  font-weight: 400;
  margin: 3px;
}

/* Input Fields - Improve Visibility */
.inputField-customizable {
  width: 100%;
  height: 8%;
  padding: 2%;
  border: 0.1vw solid #ccc !important; /* Light gray border */
  border-radius: 0.5vw;
  font-size: 1vw;
  color: #333 !important; /* Darker text for readability */
  background-color: #ffffff !important; /* Pure white background for consistency */
}

/* Input Fields Focus (Highlight Effect) */
.inputField-customizable:focus {
  border-color: #00C1A0 !important; /* Green border on focus */
  outline: none;
  background-color: #f0f8f7 !important; /* Very light greenish background for a soft effect */
  box-shadow: 0 0 1vw rgba(0, 193, 160, 0.5); /* Soft green glow */
}


/* Submit Button */
.submitButton-customizable {
  width: 100%;
  height: 10%; /* Adaptive */
  background-color: #00C1A0;
  color: #ffffff;
  font-size: 1.2vw; /* Scales with screen */
  font-weight: bold;
  border: none;
  border-radius: 0.7vw;
  text-align: center;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.submitButton-customizable:hover {
  background-color: #009f8a;
  transform: scale(1.02);
}



.idpButton-customizable {
  width: 100%;
  height: 40px;
  background-color: #5bc0de; /* Light blue */
  color: #ffffff; /* White text */
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 5px;
  text-align: center;
}
/* Error Messages */
.errorMessage-customizable {
  background-color: rgba(255, 0, 0, 0.1);
  border: 0.1vw solid #cc0000;
  color: #cc0000 !important;
  padding: 2%;
  font-size: 1vw; /* Scales */
  text-align: center;
  border-radius: 0.5vw;
}



/* Footer Text */
.legalText-customizable {
  font-size: 1vw;
  color: #747474;
  text-align: center;
  margin-top: 3%;
}


/* Text Descriptions */
.textDescription-customizable {
  color: #002B76 !important;
  font-size: 1.5vw; /* Relative to screen */
  font-weight: bold;
  text-align: center;
  letter-spacing: 0.1vw;
}






/* Password Check */
.passwordCheck-notValid-customizable {
  color: #d64958; /* Red for invalid */
}

.passwordCheck-valid-customizable {
  color: #19bf00; /* Green for valid */
}





