// import React, { useEffect, useState } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';
// import TextField from '@mui/material/TextField';

// // Define the Package type
// type Package = {
//   sectionTitle: string;
//   sectionOrder: number;
// };

// interface AddEditSectionProps {
//   open: boolean;
//   onClose: () => void;
//   section?: Package;  // section is now of type Package
//   onSubmit: (data: Package) => Promise<void>;
// }

// const AddEditSection: React.FC<AddEditSectionProps> = ({
//   open,
//   onClose,
//   section = { sectionTitle: '', sectionOrder: 0 }, // Default value for section
//   onSubmit,
// }) => {
//   const [sectionTitle, setsectionTitle] = useState(section.sectionTitle); 
//   const [sectionOrder, setsectionOrder] = useState(section.sectionOrder); 
//   const [error, setError] = useState<string | null>(null);

//   useEffect(() => {
//     setsectionTitle(section.sectionTitle);
//     setsectionOrder(section.sectionOrder);
//   }, [section]);

//   const handleSubmit = async () => {
//     try {
//       await onSubmit({ sectionTitle, sectionOrder });
//       onClose();
//     } catch (err) {
//       setError('An error occurred while submitting the form.');
//     }
//   };

//   const handleClose = () => {
//     setError(null);
//     onClose();
//   };

//   return (
//     <Dialog open={open} onClose={handleClose} fullWidth maxWidth="sm">
//       <DialogTitle>{section ? 'Edit Section' : 'Add Section'}</DialogTitle>
//       <DialogContent>
//         <TextField
//           label="Section Name"
//           fullWidth
//           multiline
//           minRows={3}
//           maxRows={10}
//           value={sectionTitle}
//           onChange={(e) => setsectionTitle(e.target.value)}
//           margin="normal"
//           error={!!error}
//           helperText={error}
//         />
//         <TextField
//           label="Section Order"
//           fullWidth
//           type="number"
//           value={sectionOrder}
//           onChange={(e) => setsectionOrder(Number(e.target.value))}
//           margin="normal"
//           error={!!error}
//           helperText={error}
//         />
//       </DialogContent>
//       <DialogActions>
//         <Button onClick={handleClose} color="primary">
//           Cancel
//         </Button>
//         <Button onClick={handleSubmit} color="primary">
//           {section ? 'Update' : 'Add'}
//         </Button>
//       </DialogActions>
//     </Dialog>
//   );
// };

// export default AddEditSection;

import React, { useState, useEffect } from 'react';
import { Dialog, DialogActions, DialogContent, DialogTitle, TextField, Button } from '@mui/material';

interface AddEditSectionProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: { sectionTitle: string; sectionOrder: number }) => void;
  section?: { sectionId: number; sectionTitle: string; sectionOrder: number }; // Optional for editing
}

const AddEditSection: React.FC<AddEditSectionProps> = ({ open, onClose, onSubmit, section }) => {
  const [sectionTitle, setSectionTitle] = useState('');
  const [sectionOrder, setSectionOrder] = useState(1);

  useEffect(() => {
    if (section) {
      setSectionTitle(section.sectionTitle);
      setSectionOrder(section.sectionOrder);
    }
  }, [section]);

  const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSectionTitle(event.target.value);
  };

  const handleOrderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSectionOrder(Number(event.target.value));
  };

  const handleSubmit = () => {
    onSubmit({ sectionTitle, sectionOrder });
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth>
      <DialogTitle>{section ? 'Edit Section' : 'Add Section'}</DialogTitle>
      <DialogContent>
        <TextField
          label="Section Title"
          fullWidth
          variant="outlined"
          value={sectionTitle}
          onChange={handleTitleChange}
          margin="normal"
        />
        <TextField
          label="Section Order"
          fullWidth
          variant="outlined"
          type="number"
          value={sectionOrder}
          onChange={handleOrderChange}
          margin="normal"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button onClick={handleSubmit} color="primary">
          {section ? 'Update Section' : 'Add Section'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddEditSection;

