import React, { useEffect } from 'react';
import DefaultLayout from '../layout/DefaultLayout';
import { useNavigate } from 'react-router-dom';
import './instructions.css';
import img_instructions from '../images/instructions.png';
import { useAuth } from 'react-oidc-context';
import config from '../config';



const Instructions: React.FC = () => {
  const navigate = useNavigate();
  const auth = useAuth(); // Access the auth context

  const getAccessToken = async () => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    console.warn('User is not authenticated. Cannot fetch token.');
    return null;
  };

  useEffect(() => {
    const fetchWelcomeData = async () => {
      const token = await getAccessToken();
      if (!token) {
        console.error('No token available. Aborting API call.');
        return;
      }
    
      try {
        const response = await fetch(config.userService.welcome, {
          method: 'GET',
          headers: {
            Accept: '*/*',
            Authorization: `Bearer ${token}`,
          },
        });
    
        if (response.ok) {
          const data = await response.json();
          // Process the data
        } else {
          const errorText = await response.text();
          console.error(
            `Failed to fetch welcome data. Status: ${response.status}, Details: ${errorText}`,
          );
        }
      } catch (error) {
        console.error('Error during API call:', error);
      }
    };
    

    fetchWelcomeData();
  }, []);

 

  const handleAssessment = async () => {
    const token = await getAccessToken();
    if (!token) {
      console.error('No access token found. Cannot start assessment.');
      return;
    }
  
    try {
      const response = await fetch(config.userService.startAssessment, {
        method: 'POST',
        headers: {
          Accept: '*/*',
          Authorization: `Bearer ${token}`,
        },
        body: '', // Empty body as specified
      });
  
      if (response.ok) {
        navigate('/questionapp'); // Navigate to the question app page
      } else {
        const errorText = await response.text();
        console.error(
          `Failed to start assessment. Status: ${response.status}, Details: ${errorText}`,
        );
      }
    } catch (error) {
      console.error('Error starting assessment:', error);
    }
  };
  
  return (
    <DefaultLayout>
      <div className="min-h-screen text-white font-sans bg-gradient-to-b from-gray-900 to-gray-800">
        {/* Main Section */}
        <main>
          <div className="top-container mx-auto text-white flex flex-col lg:flex-row items-center lg:space-x-8">
            {/* Left Section: Title and Subtitle */}
            <div className="w-full lg:w-1/2 text-left mb-6 lg:mb-0">
              <h1 className="cmmc text-4xl lg:text-5xl font-bold">CMMC</h1>
              <h2 className="btn-assessment flex items-center text-lg sm:text-xl lg:text-2xl font-semibold mt-2 p-4 sm:p-6 lg:p-8 rounded-lg">
  <span className="top-section-h1-span-level-1 mr-2">
    Level 1
  </span>
  <span className="top-section-h1-span-assessment">
    Assessment
  </span>
</h2>

            </div>

            {/* Right Section: Description */}
            <div className="w-full lg:w-1/2 text-center lg:text-left">
              <h1 className="welcome text-2xl lg:text-3xl font-bold mb-4">
                WELCOME TO THE CMMC L1 ASSESSMENT
              </h1>
              <p className="w-p text-gray-300">
                This assessment is designed to evaluate your organization's
                compliance with basic cybersecurity practices required for CMMC
                Level 1. Please answer each question accurately to reflect your
                current practices. All responses will be treated with
                confidentiality.
              </p>
            </div>
          </div>

          {/* Instructions Section */}
          <section className="m-3 bg-white text-[#0b2653] rounded-lg p-4 sm:p-8 shadow-md">
  <div className="flex flex-col gap-6 lg:flex-row">
    {/* How to Respond Section */}
    <div className="w-full lg:w-1/4">
      <h2 className="i-h mb-4">How to Respond:</h2>
      <p className="i-p mb-4">
        For each question, select the percentage that best represents the level
        of compliance or implementation for the control.
      </p>
      <div className="flex justify-between items-center text-gray-500 text-xs">
        <img
          src={img_instructions}
          alt="Raaz Security"
          className="img-instructions w-full lg:w-auto"
        />
      </div>
    </div>

    <div className="hidden lg:block">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="2"
        height="100%"
        viewBox="0 0 2 297"
        fill="none"
        className="h-full"
      >
        <path d="M1 0V297" stroke="#E1E1E1" />
      </svg>
    </div>

    {/* Navigation Section */}
    <div className="w-full lg:w-1/4">
      <h2 className="i-h mb-4">Navigation:</h2>
      <p className="i-p mb-2">
        Click Next to proceed after completing all questions on this page.
      </p>
      <ul className="list-disc list-inside i-p">
        <li>
          The left panel helps you track your progress: total questions,
          answered, and unanswered.
        </li>
        <li>
          The current domain and question set are highlighted for your
          reference.
        </li>
      </ul>
    </div>

    <div className="hidden lg:block">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="2"
        height="100%"
        viewBox="0 0 2 297"
        fill="none"
        className="h-full"
      >
        <path d="M1 0V297" stroke="#E1E1E1" />
      </svg>
    </div>

    {/* Documentation Section */}
    <div className="w-full lg:w-1/4">
      <h2 className="i-h mb-4">Documentation:</h2>
      <p className="i-p mb-4">
        Check the box if you can provide supporting documentation for the
        selected percentage.
      </p>
      <div className="flex items-center gap-2 i-p mb-2">
        <input
          type="checkbox"
          id="documentation1"
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label
          htmlFor="documentation1"
          className="text-gray-600 cursor-pointer"
        >
          Can you provide documentation for the above control?
        </label>
      </div>
    </div>

    <div className="hidden lg:block">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="2"
        height="100%"
        viewBox="0 0 2 297"
        fill="none"
        className="h-full"
      >
        <path d="M1 0V297" stroke="#E1E1E1" />
      </svg>
    </div>

    {/* Helpful Tips Section */}
    <div className="w-full lg:w-1/4">
      <h2 className="i-h mb-4">Helpful Tips:</h2>
      <p className="i-p mb-2">
        Hover over the information icons (i) next to each question for
        additional context or examples.
      </p>
      <p className="i-p">
        <span className="font-semibold">Automatic Saving:</span> All your
        responses are automatically saved as you make selections. You can exit
        and return to continue from where you left off.
      </p>
    </div>
  </div>
</section>


          {/* Start Assessment Button */}
          <div className="mt-8 mr-2 mb-6 items-center flex justify-end">
            <button
              onClick={handleAssessment}
              className="start-assessment flex items-center justify-center gap-3 bg-[#104da2] hover:bg-[#083a7d] text-white px-8 py-4 rounded-lg text-lg sm:text-xl font-bold shadow-lg transition"
            >
              Start Assessment
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="47"
                height="47"
                viewBox="0 0 47 47"
                fill="none"
              >
                <g clip-path="url(#clip0_111_70)">
                  <path
                    d="M23.5 47C36.4787 47 47 36.4787 47 23.5C47 10.5213 36.4787 0 23.5 0C10.5213 0 0 10.5213 0 23.5C0 36.4787 10.5213 47 23.5 47Z"
                    fill="#1DD8B4"
                  />
                  <path
                    d="M19.799 31.9801C19.3319 31.9801 18.8719 31.8005 18.5198 31.4483C17.8083 30.7369 17.8083 29.5942 18.5198 28.8827L23.8953 23.5072L18.5126 18.1029C17.8083 17.3914 17.8083 16.2488 18.5126 15.5373C19.2169 14.8259 20.3667 14.833 21.0782 15.5373L29.0121 23.5072L21.0782 31.4411C20.726 31.7933 20.2589 31.9729 19.799 31.9729V31.9801Z"
                    fill="white"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_111_70">
                    <rect width="47" height="47" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </button>
          </div>
        </main>
      </div>
    </DefaultLayout>
  );
};

export default Instructions;
