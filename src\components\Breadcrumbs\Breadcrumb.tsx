import { Link } from 'react-router-dom';

interface BreadcrumbProps {
  pageName: string;
  menuName: any;
}

const Breadcrumb = ({ pageName, menuName }: BreadcrumbProps) => {
  return (
    <div className="mb-4 sm:flex sm:items-center sm:justify-between">
      <nav aria-label="breadcrumb">
        <ol className="flex items-center text-gray-500">
          <li className="flex items-center font-medium text-primary hover:text-primary-dark">
            <h2 className="inline">
              {menuName}
            </h2>
          </li>
          <li className="font-medium text-primary hover:text-primary-dark ml-1">
            {pageName === "Manage Mapping" ? (
              <Link to="/new-departments" className="hover:underline">
                {pageName}
              </Link>
            ) : (
              <span>{pageName}</span>
            )}
          </li>
        </ol>
      </nav>
    </div>
  );
};

export default Breadcrumb;