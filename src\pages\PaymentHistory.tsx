import React, { useState, useEffect } from "react";
import DefaultLayout from "../layout/DefaultLayout";
import { useAuth } from "react-oidc-context";
import config from "../config";
import { Button } from "@mui/material";
import { useNavigate } from "react-router-dom";

type Payment = {
    transactionId: string;
    stripePaymentId: string;
    amount: number;
    currency: string;
    status: "completed" | "processing" | "pending";
    createdAt: string;
  };
  
const PaymentHistory: React.FC = () => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const navigate = useNavigate();
  const itemsPerPage = 5;
    const auth = useAuth();

  const getAccessToken = async (): Promise<string | null> => {
    if (auth.isAuthenticated && auth.user) {
      return auth.user.access_token;
    }
    console.error("User is not authenticated. Cannot fetch token.");
    return null;
  };
  // Fetch payment history from API
  const fetchPayments = async () => {
    setLoading(true);
    setError(null);
    const token = await getAccessToken();
    if (!token) return;
  
    try {
      const response = await fetch(
        `${config.userService.paymentHistory}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            accept: "*/*",
          },
        }
      );
  
      if (!response.ok) throw new Error(`HTTP Error: ${response.status}`);
  
      const data = await response.json();
  
      // Map response to correct state format
      const formattedData: Payment[] = data.map((payment: any) => ({
        transactionId: payment.transactionId,
        stripePaymentId: payment.stripePaymentId,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status.toLowerCase(), // Ensure consistency
        createdAt: new Date(payment.createdAt).toLocaleString(), // Format date
      }));
  
      setPayments(formattedData);
    } catch (err) {
      setError("Failed to load payment history. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchPayments();
  }, []);

  const totalPages = Math.ceil(payments.length / itemsPerPage);
  const displayedPayments = payments.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <DefaultLayout>
       <div
          className="mb-4"
          style={{
            display: 'flex',
            justifyContent: 'flex-start',
            width: '100%',
          }}
        >
          <Button
            variant="contained"
            color="secondary"
            onClick={() => navigate('/dashboard')}
            sx={{
              fontWeight: 'bold',
              fontSize: {
                xs: '12px', // Smallest screens
                sm: '14px', // Small screens (default)
                md: '16px', // Medium screens
                lg: '18px', // Large screens
              },
              textTransform: 'none',
              px: 3,
              py: 1.5,
              boxShadow: 2,
              backgroundColor: '#1976d2',
              '&:hover': {
                backgroundColor: '#155fa0',
              },
              mr: 2,
            }}
          >
            Dashboard
          </Button>
        </div>
    <div className="max-w-5xl mx-auto p-6 bg-white shadow-lg rounded-lg">
      <h2 className="text-2xl font-bold text-gray-800 mb-4">Payment History</h2>

      {loading ? (
        <p className="text-gray-600 text-center">Loading payments...</p>
      ) : error ? (
        <p className="text-red-600 text-center">{error}</p>
      ) : payments.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-white text-left text-sm text-gray-700 shadow-md rounded-md">
            <thead className="bg-gray-100 text-gray-600 uppercase text-sm">
              <tr>
                <th className="px-4 py-3">Transaction ID</th>
                <th className="px-4 py-3">Amount</th>
                <th className="px-4 py-3">Currency</th>
                <th className="px-4 py-3">Status</th>
                <th className="px-4 py-3">Date</th>
              </tr>
            </thead>
            <tbody>
  {displayedPayments.map((payment) => (
    <tr key={payment.transactionId} className="hover:bg-gray-50 border-b">
      <td className="px-4 py-3 font-mono text-blue-600">{payment.transactionId}</td>
      <td className="px-4 py-3 font-semibold">${payment.amount.toFixed(2)}</td>
      <td className="px-4 py-3">{payment.currency.toUpperCase()}</td>
      <td className="px-4 py-3">
        <span
          className={`px-2 py-1 text-xs font-bold rounded ${
            payment.status === "completed"
              ? "bg-green-100 text-green-600"
              : payment.status === "processing"
              ? "bg-yellow-100 text-yellow-600"
              : "bg-red-100 text-red-600"
          }`}
        >
          {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
        </span>
      </td>
      <td className="px-4 py-3">{payment.createdAt}</td>
    </tr>
  ))}
</tbody>

          </table>
        </div>
      ) : (
        <p className="text-gray-600 text-center">No payment history available.</p>
      )}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center mt-4">
          <button
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md disabled:opacity-50"
          >
            Previous
          </button>
          <span className="text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
    </DefaultLayout>
  );
};

export default PaymentHistory;
